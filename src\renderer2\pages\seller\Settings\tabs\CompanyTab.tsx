import React, { useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import clsx from 'clsx';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import axios from 'axios';
import { useGlobalStore, useSellerSettingStore } from '@bryzos/giss-ui-library';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import { CustomMenu } from 'src/renderer2/pages/buyer/CustomMenu';
import { companySchema } from '../schemas/companySchema';
import { EmailTagInputField } from 'src/renderer2/component/EmailTagInput';
import SingleStateSelector from 'src/renderer2/pages/buyer/newSettings/components/StateSelector/SingleStateSelector';

interface InputFocusState {
    parentCompanyName: boolean;
    companyType: boolean;
    companyAddress: boolean;
    arContactName: boolean;
    arContactEmail: boolean;
    sendInvoicesTo: boolean;
    sendOrderDocsTo: boolean;
}

const CompanyTab: React.FC<{setSaveFunctions: any}> = ({ setSaveFunctions}) => {
    const {
        register,
        handleSubmit,
        clearErrors,
        setError,
        setValue,
        reset,
        watch,
        control,
        getValues,
        trigger,
        resetField,
        formState: { errors, dirtyFields, isDirty, isValid, isSubmitting  },
        getFieldState,
      } = useForm({
        resolver: yupResolver(companySchema),
        mode: 'onSubmit',
      });
    
    const selectCompanyHQAddressRef = useRef(null);
    const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
        parentCompanyName: false,
        companyType: false,
        companyAddress: false,
        arContactName: false,
        arContactEmail: false,
        sendInvoicesTo: false,
        sendOrderDocsTo: false,
    });;
    const {referenceData}: any = useGlobalStore();
    const [states, setStates] = useState<any[]>([]);
    const { mutateAsync: saveUserSettings } = useSaveUserSettings();
    const { sellerSettings , setShowFadeLoader }: any = useSellerSettingStore();
    const [isAddressContainerClicked, setIsAddressContainerClicked] = useState(false);
    const [isStateSelectorFocused, setIsStateSelectorFocused] = useState(false);
    const addressContainerRef = useRef<HTMLDivElement>(null);
    const line1InputRef = useRef<HTMLInputElement>(null);
    const isButtonDisabled =  isSubmitting || !isDirty;

    useEffect(() => {
        if(sellerSettings) {
            setValue('parentCompanyName', sellerSettings?.company_name || '');
            setValue('companyType', sellerSettings?.company_type || '');
            setValue('companyAddress', {
              line1: sellerSettings?.company_address?.line1 || '',
              line2: sellerSettings?.company_address?.line2 || '',
              city: sellerSettings?.company_address?.city || '',
              state: sellerSettings?.company_address?.state_id || '',
              stateCode: sellerSettings?.company_address?.state_code || '',
              zip: sellerSettings?.company_address?.zip || '',
            });
            setValue('arContactName', sellerSettings?.ar_contact_name || '');
            setValue('arContactEmail', sellerSettings?.ar_email_id || '');
            setValue('sendInvoicesTo', sellerSettings?.send_invoices_to || '<EMAIL>');
            setValue('sendOrderDocsTo', sellerSettings?.shipping_docs_to || '<EMAIL>');
        }
    }, [sellerSettings]);

    useEffect(() => {
        setSaveFunctions({
            onSave: () => handleSubmit(handleSaveCompany)(),
            isDisabled: isButtonDisabled,
        });
    }, [isButtonDisabled, handleSubmit]);

    useEffect(() => {   
        setTimeout(() => {
            const parentCompanyNameInput = document.getElementById('parentCompanyName');
            if (parentCompanyNameInput) {
                parentCompanyNameInput.focus();
            }
        }, 100)
    }, []);

    useEffect(() => {
        if (isAddressContainerClicked && line1InputRef.current) {
          // Small delay to ensure the input is rendered
          setTimeout(() => {
            line1InputRef.current?.focus();
          }, 0);
        }
      }, [isAddressContainerClicked]);

    useEffect(() => {
        if(referenceData?.ref_states) {
            setStates(referenceData?.ref_states)
        }
    }, [referenceData])

    const handleInputFocus = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: true,
        }));
    };

    const handleInputBlur = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: false,
        }));
    };

    const companyTypes = [
        { title: 'Fabricator', value: 'Fabricator' },
        { title: 'Constructor', value: 'Constructor' },
        { title: 'Distributor', value: 'Distributor' },
        { title: 'OEM', value: 'OEM' },
    ];

    const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
        try {
            if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
                const payload = {
                data: {
                    state_id: Number(getValues(stateCode)),
                    zip_code: parseInt(getValues(zipCode)),
                },
            };
            const checkStateZipResponse = await axios.post(
                import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
                payload
            );
            if (checkStateZipResponse.data.data === true) {
                clearErrors([stateCode, zipCode]);
                return true;
            } else {
                setError(stateCode, { message: "The zip code and state code do not match" });
                setError(zipCode, { message: "The zip code and state code do not match" });
                return false;
            }
        }
     } catch (error) {
            console.error(error)
        }
    };
    useEffect(() => {
        handleStateZipValidation('companyAddress.zip', 'companyAddress.state')
    }, [watch('companyAddress.state'), watch('companyAddress.zip')])

    const handleShipmentAddressContainerClickAway = () => {
        handleInputBlur('companyAddress')
        if (!isStateSelectorFocused) {
          if(!(errors?.companyAddress?.line1 || errors?.companyAddress?.line2 || errors?.companyAddress?.city || errors?.companyAddress?.state || errors?.companyAddress?.zip || errors?.companyAddress?.stateCode)){
            setIsAddressContainerClicked(false)
          }else{
            setIsAddressContainerClicked(true)
          }
        }
      }
      // Custom clickaway handler
      useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
          if (!isAddressContainerClicked) return;
          
          const target = event.target as HTMLElement;
          
          // Check if click is inside the address container
          if (addressContainerRef.current && addressContainerRef.current.contains(target)) {
            return;
          }
          
          // Check if click is inside any state selector
          const stateSelectorElement = document.querySelector('[data-state-selector]');
          if (stateSelectorElement && stateSelectorElement.contains(target)) {
            return;
          }
          
          // If we get here, the click was outside both the container and state selector
          handleShipmentAddressContainerClickAway();
        };
    
        // Add event listener when address container is clicked
        if (isAddressContainerClicked) {
          document.addEventListener('mousedown', handleClickOutside);
        }
    
        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }, [isAddressContainerClicked]);

      useEffect(() => {
        if(errors?.companyAddress){
            setIsAddressContainerClicked(true)
        }
      }, [errors])

      const handleSaveCompany = async (data: any) => {
        try{
            const isZipValid = await handleStateZipValidation('companyAddress.zip', 'companyAddress.state')
            if(!isZipValid){
                return
            }
            setShowFadeLoader(true)
            const payload = {
                company_name: data.parentCompanyName,
                company_type: data.companyType,
                company_address: {
                    line1: data.companyAddress.line1,
                    line2: data.companyAddress.line2?.trim() || null,
                    city: data.companyAddress.city,
                    state_id: Number(data.companyAddress.state),
                    zip: data.companyAddress.zip
                },
                ar_contact_name: data.arContactName,
                ar_email_id: data.arContactEmail,
                send_invoices_to: data.sendInvoicesTo,
                shipping_docs_to: data.sendOrderDocsTo,
            }
            await saveUserSettings({ route: 'user/seller/settings/company', data: payload })
            setShowFadeLoader(false)
            setTimeout(() => {
                reset(data); 
            }, 100)
        }catch(err){
            console.error(err)
            setShowFadeLoader(false)
        }
    }

    return (
        <div className={styles.tabContent} ref={selectCompanyHQAddressRef}>
            <div className={styles.formContainer}>
                {/* PARENT COMPANY NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label htmlFor="parentCompanyName">
                        Company Name
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span className={clsx(styles.defaultInput)}
                        id='parentCompanyName'
                        tabIndex={0} 
                        onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: false,
                            }));
                        }}
                        >{watch('parentCompanyName')}</span>
                    </span>
                </div>

                     {/* COMPANY TYPE */}
                     <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyType && styles.focusLbl)} htmlFor="companyType">
                        company type
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <CustomMenu
                            onfocus={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyType: true,
                                }));
                            }}
                            onBlur={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyType: false,
                                }));
                            }}
                            control={control}
                            name={'companyType'}
                            // placeholder={'Company Type'}
                            MenuProps={{
                                classes: {
                                    paper: styles.Dropdownpaper,
                                    list: styles.muiMenuList,
                                    select: styles.selectClassName,
                                },
                                id: 'companyTypeMenu'
                            }}
                            onFocusKeyDown={() => {
                                setIsAddressContainerClicked(true)
                            }}
                            className={styles.selectDropdown}
                            placeholderClass={styles.placeholderTxt}
                            IconComponent={DropdownIcon}
                            items={companyTypes}
                            renderValue={(value: string) => {
                                const selectedType = companyTypes.find(type => type.value === value);
                                return (
                                    <span>
                                        {selectedType?.title}
                                    </span>
                                );
                            }}

                        />
                    </span>
                </div>

                                    {/* COMPANY HQ ADDRESS */}
                                    <div className={ clsx(styles.formGroupInput, styles.companyHQAddressContainer)}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyAddress && styles.focusLbl)} htmlFor="companyAddress">
                            Company Address
                        </label>
                    </span>
                    <span className={clsx(styles.col1, styles.locationAddressContainer)}>
                        {
                            (isAddressContainerClicked || errors?.companyAddress) ? (
                            <div className={clsx(styles.customAddressContainer)} ref={addressContainerRef}>
                                <span className={clsx(styles.addresInputMain)}>
                                 <InputWrapper>
                                    <CustomTextField
                                        className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line1 && styles.error)}
                                        type='text'
                                        register={register('companyAddress.line1')}
                                        placeholder='Address 1'
                                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                            register('companyAddress.line1').onBlur(e);
                                            handleInputBlur('companyAddress')
                                        }}
                                        onFocus={() => handleInputFocus('companyAddress')}
                                        errorInput={errors?.companyAddress?.line1}
                                        inputRef={(e: any) => {
                                            line1InputRef.current = e;
                                          }}
                                    />
                                </InputWrapper>
                                </span>
                              
                                 <span className={clsx(styles.addresInputMain)}>
                                      <InputWrapper>
                                    <CustomTextField
                                        className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line2 && styles.error)}
                                        type='text'
                                        // autoFocus={true}
                                        register={register('companyAddress.line2')}
                                        placeholder='Address 2'
                                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                            register('companyAddress.line2').onBlur(e);
                                            handleInputBlur('companyAddress')
                                        }}
                                        onFocus={() => handleInputFocus('companyAddress')}
                                        errorInput={errors?.companyAddress?.line2}
                                    />
                                </InputWrapper>

                                 </span>
                            

                                <span className={styles.zipInputContainer}>
                                    <span className={clsx(styles.col1,styles.addresInputMain)}>
                                        <InputWrapper>
                                            <CustomTextField
                                                className={clsx(styles.inputCreateAccount, errors?.companyAddress?.city && styles.error)}
                                                type='text'
                                                register={register('companyAddress.city')}
                                                placeholder='City'
                                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                                    register('companyAddress.city').onBlur(e);
                                                    handleInputBlur('companyAddress')
                                                }}
                                                onFocus={() => handleInputFocus('companyAddress')}
                                                errorInput={errors?.companyAddress?.city}
                                            />
                                        </InputWrapper>
                                    </span>
                                    <span className={clsx(styles.inputSection, styles.yourLocationAdressState, styles.col2, styles.bdrRadius0, styles.bdrRight0)}>
                                        <Controller
                                            name="companyAddress.state"
                                            control={control}
                                            render={({ field }) => (
                                                <>
                                                <SingleStateSelector
                                                    states={states.map((state: any) => ({ state_code: state.code }))}
                                                    value={field.value}
                                                    onChange={(stateCode) => {
                                                        const selectedState = states.find((state: any) => state.code === stateCode);
                                                        if (selectedState) {
                                                        field.onChange(selectedState.id);
                                                        setValue('companyAddress.stateCode', selectedState.code);
                                                        // Clear any exis ting errors for the state field
                                                        if (errors?.companyAddress?.state) {
                                                            clearErrors('companyAddress.state');
                                                        }
                                                        // Trigger validation after setting the value
                                                        setTimeout(() => {
                                                            trigger('companyAddress.state');
                                                        }, 0);
                                                        } else {
                                                        console.error('State not found for code:', stateCode);
                                                        }
                                                    }}
                                                    onBlur={() => {
                                                        field.onBlur();
                                                        handleInputBlur('companyAddress');
                                                    }}
                                                    error={!!errors?.companyAddress?.state}
                                                    placeholder="State"
                                                    stateIdToCode={(stateId) => {
                                                        const state = states.find((s: any) => s.id === stateId);
                                                        return state ? state.code : watch('companyAddress.stateCode');
                                                    }}
                                                    onFocus={() => handleInputFocus('companyAddress')}
                                                    />

                                                    </>
                                            )}
                                        />

                                    </span>
                                    <span className={clsx(styles.col3,styles.addresInputMain)}>
                                        <InputWrapper>
                                            <CustomTextField
                                                className={clsx(styles.inputCreateAccount, (errors?.companyAddress?.zip || errors?.companyAddress?.state) && styles.error)}
                                                type='text'
                                                maxLength={5}
                                                register={register('companyAddress.zip')}
                                                placeholder='Zip Code'
                                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                                    register('companyAddress.zip').onBlur(e);
                                                    handleInputBlur('companyAddress');
                                                }}
                                                onFocus={() => handleInputFocus('companyAddress')}
                                                errorInput={errors?.companyAddress?.zip || errors?.companyAddress?.state}
                                                mode="wholeNumber"
                                                onKeyDown={(e) => {
                                                    if(e.key === 'Tab'){
                                                        if(!e.shiftKey){
                                                            handleShipmentAddressContainerClickAway()
                                                        }
                                                    }
                                                }}
                                            />
                                        </InputWrapper>
                                    </span>
                                </span>

                            </div>
                            ) : (
                                <div className={styles.addressDisplayContainer} onClick={() => setIsAddressContainerClicked(true)}>
                                    {
                                        (watch('companyAddress.line1') || watch('companyAddress.line2') || watch('companyAddress.city') || watch('companyAddress.state') || watch('companyAddress.zip')) ? (
                                            <div className={styles.valueDiv}>
                                                <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('companyAddress.line1') ? `${watch('companyAddress.line1')}` : ''}</p>
                                                <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('companyAddress.line2') ? `${watch('companyAddress.line2')}` : ''}</p>
                                                <span className={styles.lastAddressFiled}>
                                                    <p className={clsx(styles.addressInputsCol1, styles.hideInputBackground)}>{watch('companyAddress.city') ? `${watch('companyAddress.city')}` : ''}</p>
                                                    <p className={clsx(styles.addressInputsCol2, styles.hideInputBackground)}>{watch('companyAddress.stateCode') ? `${watch('companyAddress.stateCode')}` : ''}</p>
                                                    <p className={clsx(styles.addressInputsCol3, styles.hideInputBackground)}>{watch('companyAddress.zip') ? `${watch('companyAddress.zip')}` : ''}</p>
                                                </span>
                                            </div>
                                        ) : (
                                            <span className={clsx(styles.valueDiv,styles.placeHolderDiv)} onClick={() => setIsAddressContainerClicked(true)}></span>
                                        )
                                    }
                                </div>
                            )
                        }
                    </span>
                </div>



                {/* AR CONTACT NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx((isInputFocused.arContactName) && styles.focusLbl)} htmlFor="arContactName">
                            AR CONTACT NAME
                        </label>
                    </span>
                    <span className={clsx(styles.col1, styles.inputMain)}>
                        <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, errors?.arContactName && styles.error)}
                                type='text'
                                register={register("arContactName")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("arContactName").onBlur(e);
                                    handleInputBlur('arContactName')
                                }}
                                onFocus={() => handleInputFocus('arContactName')}
                                errorInput={errors?.arContactName}
                                onKeyDown={(e) => {
                                    if(e.key === 'Tab'){
                                        if(e.shiftKey){
                                            setIsAddressContainerClicked(true)
                                        }
                                    }
                                }}
                            />
                        </InputWrapper>
                    </span>
                </div>

                {/* AR CONTACT EMAIL */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.arContactEmail && styles.focusLbl)} htmlFor="arContactEmail">
                            AR CONTACT EMAIL
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <EmailTagInputField
                            value={watch('arContactEmail') ? watch('arContactEmail').split(',').filter(Boolean) : []}
                            onChange={(emails) => {
                                setValue('arContactEmail', emails.join(','), {
                                    shouldDirty: true,
                                    shouldTouch: true
                                });
                                clearErrors('arContactEmail')
                            }}
                            placeholder=""
                            maxEmails={5}
                            register={register("arContactEmail")}
                            error={errors?.arContactEmail?.message}
                            onBlur={() => {
                                handleInputBlur('arContactEmail')
                            }}
                            onFocus={() => {
                                handleInputFocus('arContactEmail')
                            }}
                            inputBlur={() => {
                                handleInputBlur('arContactEmail')
                            }}
                            control={control}
                            onKeyDown={(e) => {
                                if(e.key === 'Tab'){
                                    e.preventDefault();
                                    const saveButton = document.getElementById('settings-save-button') as HTMLButtonElement;
                                    if (saveButton) {
                                        saveButton.focus();
                                    }
                                }
                            }}
                        />
                    </span>
                </div>

                {/* SEND INVOICES TO */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.sendInvoicesTo && styles.focusLbl)} htmlFor="sendInvoicesTo">
                            SEND INVOICES TO
                        </label>
                    </span>
                    <span className={styles.col1}>
                    <span className={clsx(styles.defaultInput)} onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendOrderDocsTo: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendOrderDocsTo: false,
                            }));
                        }}
                        >
                            {watch('sendInvoicesTo') ?? "<EMAIL>"}
                        </span>
                    </span>
                </div>

                {/* SEND ORDER DOCS TO */}
                <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.sendOrderDocsTo && styles.focusLbl)} htmlFor="sendOrderDocsTo">
                        SEND ORDER DOCS TO
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span className={clsx(styles.defaultInput)} onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendOrderDocsTo: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendOrderDocsTo: false,
                            }));
                        }}
                        >
                            {watch('sendOrderDocsTo') ?? "<EMAIL>"}
                        </span>
                    </span>
                </div>
            </div>
        </div>
    );
};

export default CompanyTab; 
