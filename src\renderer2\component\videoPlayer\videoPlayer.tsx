// @ts-nocheck
import React, { useEffect, useRef, useState } from 'react';
import { ReactComponent as CloseXIcon } from '../../assets/New-images/Close-x-icon.svg';
import styles from './videoPlayer.module.scss';
import { ReactComponent as VideoPlayIcon } from '../../assets/New-images/mainWindowVideoPlayButton.svg';
import { ReactComponent as VideoPlayControlIcon } from '../../assets/images/Play.svg';
import { ReactComponent as VideoPauseIcon } from '../../assets/images/Pause.svg';
import { ReactComponent as VolumeIcon } from '../../assets/images/UnMute.svg';
import { ReactComponent as VolumeMutedIcon } from '../../assets/images/Mute.svg';
import { ReactComponent as FullScreenIcon } from '../../assets/images/Fullscreen.svg';
import { ReactComponent as ExitFullScreenIcon } from '../../assets/images/ExitFullScreen.svg';
import { ReactComponent as PipIcon } from '../../assets/New-images/New-Image-latest/icon-popout.svg';
import { ReactComponent as PlayNext } from '../../assets/images/Skip.svg';
import clsx from 'clsx';
import { getChannelWindow, useGlobalStore } from '@bryzos/giss-ui-library';
import { CircularProgress } from '@mui/material';
import { useLoadSubtitle } from 'src/renderer2/hooks/useLoadSubtitle';
import { ReactComponent as RightWindowPlayBtn } from '../../assets/New-images/RightWindowVideoPlayBtn.svg';
import { useVideoStore } from 'src/renderer2/pages/VideoLibrary/VideoStore';

const VideoPlayer = ({
  url,
  width,
  height,
  autoPlay,
  playNextVideo,
  disableNextVideoBtn,
  captionUrl,
  showPiPControl = false,
  hideControls = false,
  isRightWindow = false,
  seekTime = 0,            // ✨ used for initial seek (seconds)
  pauseImageUrl,
  onPiPClick = () => {},
  isPiPMode = false,
  showCloseButton = false,
  onClose = () => {},
}) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const containerRef = useRef(null);

  const [isPlaying, setIsPlaying] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMacDevice, setIsMacDevice] = useState(false);
  const [isBuffering, setIsBuffering] = useState(true);
  const [subtitleUrl, setSubtitleUrl] = useState<string | undefined>();

  const channelWindow = getChannelWindow();
  const { emitAppCloseEvent, setEmitAppCloseEvent }: any = useGlobalStore();
  const { data: subtitle, error, isLoading: isSubtitleLoading } = useLoadSubtitle(captionUrl);
  const { showPiP, setShowPiP, setSeekTime } = useVideoStore();
  const [pipMode, setPipMode] = useState(false);

  // ✨ NEW: track whether we've already performed the initial seek for the current URL
  const didInitialSeekRef = useRef(false);

  // ---- Subtitles: create & clean up Blob URL safely ----
  useEffect(() => {
    let objectUrl: string | undefined;
    if (!isSubtitleLoading && subtitle) {
      const subtitleBlob = new Blob([subtitle], { type: 'text/vtt' });
      objectUrl = URL.createObjectURL(subtitleBlob);
      setSubtitleUrl(objectUrl);
    } else {
      setSubtitleUrl(undefined);
    }
    return () => {
      if (objectUrl) URL.revokeObjectURL(objectUrl);
    };
  }, [isSubtitleLoading, subtitle]);

  useEffect(() => {
    setPipMode(isPiPMode);
  }, [isPiPMode]);

  const togglePlay = () => {
    const v = videoRef.current;
    if (!v) return;
    if (v.paused) {
      v.play?.();
      setIsBuffering(false);
      setIsPlaying(true);
    } else {
      v.pause();
      setIsPlaying(false);
    }
  };

  const updateTime = () => {
    const v = videoRef.current;
    if (!v) return;
    setCurrentTime(v.currentTime || 0);
    setDuration(v.duration || 0);
  };

  const handleSeek = (e) => {
    const v = videoRef.current;
    if (!v || !duration) return;
    const pct = Number(e.target.value);
    const t = (pct / 100) * duration;
    v.currentTime = t;
    setCurrentTime(t);
  };

  const handleVolumeChange = (e) => {
    const v = videoRef.current;
    if (!v) return;
    const vol = parseFloat(e.target.value);
    setVolume(vol);
    v.volume = vol;
    if (vol === 0) {
      setIsMuted(true);
      v.muted = true;
    } else {
      v.muted = false;
      setIsMuted(false);
    }
  };

  const toggleMute = () => {
    const v = videoRef.current;
    if (!v) return;
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    v.muted = newMuted;
    if (newMuted) setVolume(0);
    else setVolume(v.volume);
  };

  const formatTime = (time) => {
    if (isNaN(time)) return '00:00';
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);
    return hours > 0
      ? `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      : `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const toggleFullscreen = () => {
    const container = containerRef.current as any;
    if (!container) return;

    if (!isFullscreen) {
      if (channelWindow?.resizeWindow && !isMacDevice)
        (window as any).electron.send({ channel: channelWindow.resizeWindow, data: true });
      if (container.requestFullscreen) container.requestFullscreen();
      else if (container.mozRequestFullScreen) container.mozRequestFullScreen();
      else if (container.webkitRequestFullscreen) container.webkitRequestFullscreen();
      else if (container.msRequestFullscreen) container.msRequestFullscreen();
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) document.exitFullscreen();
      else if ((document as any).mozCancelFullScreen) (document as any).mozCancelFullScreen();
      else if ((document as any).webkitExitFullscreen) (document as any).webkitExitFullscreen();
      else if ((document as any).msExitFullscreen) (document as any).msExitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
  };

  const handleOnPlaying = () => setIsBuffering(false);
  const handleOnWaiting = () => setIsBuffering(true);

  // ---- Detect platform & wire fullscreen events ----
  useEffect(() => {
    if (channelWindow?.systemVersion) {
      const deviceType = (window as any).electron.sendSync({ channel: channelWindow.systemVersion });
      setIsMacDevice(deviceType === 'Mac Intel' || deviceType === 'Mac ARM');
    }

    const handleFullscreenChange = () => {
      const _isFullscreen =
        !!document.fullscreenElement ||
        !!(document as any).webkitFullscreenElement ||
        !!(document as any).mozFullScreenElement ||
        !!(document as any).msFullscreenElement;
      setIsFullscreen(_isFullscreen);
      if (channelWindow?.windowFullScreen && isMacDevice)
        (window as any).electron.send({ channel: channelWindow.windowFullScreen, data: _isFullscreen });
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange as any);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange as any);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange as any);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange as any);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange as any);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange as any);
    };
  }, [isFullscreen]);

  useEffect(() => {
    if (!isFullscreen && channelWindow?.resizeWindow && !isMacDevice)
      (window as any).electron.send({ channel: channelWindow.resizeWindow, data: false });
  }, [isFullscreen]);

  // ---- Pause when app-close is emitted from global store ----
  useEffect(() => {
    const v = videoRef.current;
    if (emitAppCloseEvent && v) {
      if (!v.paused) {
        v.pause();
        setIsPlaying(false);
        setEmitAppCloseEvent(false);
      }
    }
  }, [emitAppCloseEvent]);

  // ---- On URL change, set initial flags & reset initial-seek guard ----
  useEffect(() => {
    if (url) {
      setIsPlaying(!!autoPlay);
      setIsBuffering(!!autoPlay);
      didInitialSeekRef.current = false; // ✨ reset for new URL
    }
  }, [url, autoPlay]);

  // ---- ✨ Initial seek implementation (runs once per URL) ----
  const tryInitialSeek = async () => {
    const v = videoRef.current;
    if (!v || didInitialSeekRef.current) return;

    // If no positive seekTime, mark as done and bail (use normal behavior)
    if (!seekTime || seekTime <= 0) {
      didInitialSeekRef.current = true;
      return;
    }

    const d = v.duration;
    if (!Number.isFinite(d) || d <= 0) return; // wait for metadata

    // Clamp to [0, duration - epsilon] to avoid ending the video immediately
    const target = Math.max(0, Math.min(seekTime, Math.max(0, d - 0.05)));

    try {
      // If autoplay, ensure we start from target; if it already started, pause → set time → play
      if (autoPlay) {
        v.pause();
        v.currentTime = target;
        setCurrentTime(target);
        setDuration(d);
        setIsBuffering(true);
        await v.play();
        setIsPlaying(true);
      } else {
        // If not autoplay, land paused at target with overlay/paused-image visible
        v.pause();
        v.currentTime = target;
        setCurrentTime(target);
        setDuration(d);
        setIsPlaying(false);
        setIsBuffering(false);
      }
      didInitialSeekRef.current = true;
    } catch {
      // If autoplay is blocked by the browser, remain paused at the target
      setIsPlaying(false);
      setIsBuffering(false);
      didInitialSeekRef.current = true;
    }
  };

  // Listen for the earliest reliable moment to seek (metadata/canplay both used for robustness)
  useEffect(() => {
    const v = videoRef.current;
    if (!v) return;

    const onLoadedMetadata = () => tryInitialSeek();
    const onCanPlay = () => tryInitialSeek();

    v.addEventListener('loadedmetadata', onLoadedMetadata);
    v.addEventListener('canplay', onCanPlay);

    // If metadata is already loaded (fast cache), attempt immediately
    if (v.readyState >= 1) tryInitialSeek();

    return () => {
      v.removeEventListener('loadedmetadata', onLoadedMetadata);
      v.removeEventListener('canplay', onCanPlay);
    };
  }, [url, seekTime, autoPlay]); // rewire listeners when URL/seek/autoplay changes

  const progress = (currentTime / duration) * 100 || 0;

  // Show paused-image only when paused and not buffering
  const showPauseImage = !!pauseImageUrl && !isPlaying && !isBuffering;

  const handlePiPClick = () => {
    const v = videoRef.current;
    //pause video
    const now = v ? v.currentTime ?? 0 : 0;   // seconds (float)
    try { if(!onPiPClick?.(now)) return; } catch {}
    v?.pause();
    // fire your callback (you can wire this to your store)
    // also log for quick verification
    console.log('[VideoPlayer] PiP clicked at time (s):', now);
    setSeekTime(now);

    // keep existing PiP UI toggle behavior
    setPipMode((prev) => !prev);
    setShowPiP((prev) => !prev);
  };

  if (pipMode) {
    return (
      <div className={clsx(styles['videoPlayerMain'], 'subTitleStyle')} ref={containerRef}>
        <div className={clsx(styles['custom-video-player'], isFullscreen && styles['fullScreenHeight'])}>
          {pauseImageUrl && (
            <img
              src={pauseImageUrl}
              alt=""
              className={clsx(styles.pausedImage, styles.visible)}
              width={width}
              height={height}
              draggable={false}
            />
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={clsx(styles['videoPlayerMain'], 'subTitleStyle')} ref={containerRef}>
      <div className={clsx(styles['custom-video-player'], isFullscreen && styles['fullScreenHeight'])}>
        {showCloseButton && (
          
          <div 
            style={{
              'position': 'absolute',
              'right': '4px',
              'top': '4px',
              'zIndex': '100',
              'cursor': 'pointer',
            }} 
            className={styles.closeButton} 
            onClick={()=>{
              const v = videoRef.current;
              const now = v ? v.currentTime ?? 0 : 0;
              onClose(now);
            }}>
            <CloseXIcon />
          </div>
        )}
        {isBuffering && (
          <div className={styles.videoLoading}>
            <CircularProgress classes={{ circle: styles.colorLoader }} />
          </div>
        )}

        {/* Paused image layer */}
        {pauseImageUrl && (
          <img
            src={pauseImageUrl}
            alt=""
            className={clsx(styles.pausedImage, showPauseImage ? styles.visible : styles.hidden)}
            width={width}
            height={height}
            draggable={false}
          />
        )}

        {/* Video element (visually hidden when paused-image is shown) */}
        <video
          ref={videoRef}
          controls={false}
          onTimeUpdate={updateTime}
          onLoadedMetadata={updateTime}
          className={clsx(styles.video, showPauseImage && styles.videoHidden)}
          autoPlay={autoPlay}
          onEnded={handleEnded}
          width={width}
          height={height}
          src={url}
          onWaiting={handleOnWaiting}
          onPlaying={handleOnPlaying}
          // preload ensures metadata arrives ASAP for seeking
          preload="metadata"
        >
          {subtitleUrl && <track kind="subtitles" src={subtitleUrl} srcLang="en" label="English" default />}
          Your browser does not support the video tag.
        </video>

        {/* Overlay click toggles play/pause */}
        <div
          className={clsx(styles['overlay'], { [styles['noOverlay']]: isPlaying })}
          onClick={togglePlay}
        >
          <span
            className={clsx(
              !isRightWindow ? styles['VideoPlayIcon'] : styles['rightWindowPlayIcon'],
              styles.playIconAlpha
            )}
          >
            {!isBuffering && !isPlaying && (isRightWindow ? <RightWindowPlayBtn /> : <VideoPlayIcon />)}
          </span>
        </div>

        {!hideControls && (
          <div className={styles['controls']}>
            <div className={styles['seek-container']}>
              <input
                type="range"
                min="0"
                max="100"
                value={progress}
                className={styles['seek-bar']}
                onChange={handleSeek}
                style={{ '--progress': `${progress}%` } as React.CSSProperties}
              />
            </div>
            <div className={styles['action-container']}>
              <div className={styles['leftbar-action']}>
                <button onClick={togglePlay}>
                  {isPlaying ? <VideoPauseIcon /> : <VideoPlayControlIcon />}
                </button>
                <button className={styles.playNextBtn} onClick={playNextVideo} disabled={disableNextVideoBtn}>
                  <PlayNext />
                </button>
                <div className={styles['time-display']}>
                  <span className={styles['current-time']}>{formatTime(currentTime)}</span>
                  <span>/</span>
                  <span className={styles['duration']}>{formatTime(duration)}</span>
                </div>

                <button className={styles['volume-icon']} onClick={toggleMute}>
                  {isMuted ? <VolumeMutedIcon /> : <VolumeIcon />}
                </button>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={volume}
                  onChange={handleVolumeChange}
                  className={styles['volume-bar']}
                  style={{
                    background: `linear-gradient(to right, #fff ${volume * 100}%, rgba(156, 163, 175, 0.6) ${volume * 100}%)`,
                  }}
                />

                {showPiPControl && (
                  <button
                    className={styles.volumeIcon}
                    onClick={handlePiPClick}
                  >
                    <PipIcon />
                  </button>
                )}
              </div>
              {/* Fullscreen button (kept commented if you don't want it visible) */}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoPlayer;
