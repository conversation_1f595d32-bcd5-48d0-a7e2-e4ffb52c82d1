.modal {
  background: linear-gradient(180deg, #2a2a2a 0%, #1f1f1f 100%);
  border-radius: 12px;
  padding: 32px;
  width: 100%;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  border: 1px solid #3a3a3a;
  max-width: 500px;
  margin: 0 auto;
  z-index: 9999999;
}

.closeButton {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: #888;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.title {
  color: #ffffff;
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin: 0 0 24px 0;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.versionInfo {
  margin-bottom: 24px;
  text-align: center;

  p {
    color: #ffffff;
    margin: 8px 0;
    font-size: 16px;
    line-height: 1.5;
  }
}

.updateContents {
  margin-bottom: 32px;

  h3 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 16px 0;
    text-align: center;
  }

  // Style for release notes content
  :global(.releaseNotesContent) {
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    li {
      list-style: none;
      padding: 4px 0;
      margin: 0;
      display: flex;
      align-items: center;
      
      // Add arrow icon using CSS
      &::before {
        content: "";
        background-image: url('../../assets/New-images/icon-right-arrow.svg');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        width: 16px;
        height: 16px;
        margin-right: 8px;
        display: inline-block;
        filter: brightness(0) invert(1); // Makes the SVG white
      }
    }
  }
}

.featureList {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    display: flex;
    align-items: center;
    color: #ffffff;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 12px;
    padding: 8px 0;
  }
}

.featureIcon {
  color: #4a9eff;
  font-size: 18px;
  margin-right: 12px;
  font-weight: bold;
  display: inline-block;
  width: 20px;
  text-align: center;
}

.actionButtons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.keepCurrentButton,
.updateAppButton {
  padding: 14px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 140px;
}

.keepCurrentButton {
  background-color: #2a2a2a;
  color: #ffffff;
  border: 1px solid #3a3a3a;

  &:hover {
    background-color: #3a3a3a;
    border-color: #4a4a4a;
  }
}

.updateAppButton {
  background-color: #ffffff;
  color: #1f1f1f;

  &:hover {
    background-color: #f0f0f0;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
  }
}

// Responsive design
@media (max-width: 600px) {
  .modal {
    padding: 24px;
    margin: 20px;
  }

  .title {
    font-size: 20px;
  }

  .actionButtons {
    flex-direction: column;
    align-items: center;
  }

  .keepCurrentButton,
  .updateAppButton {
    width: 100%;
    max-width: 200px;
  }
}
