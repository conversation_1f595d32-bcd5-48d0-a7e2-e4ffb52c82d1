@use 'sass:math';

@function strip-unit($value) {
  @return math.div($value, $value * 0 + 1);
}

@mixin fluid-size($property, $min-vw, $max-vw, $min-size, $max-size) {
  #{$property}: clamp(
    $min-size,
    calc(
      #{$min-size} + #{strip-unit($max-size - $min-size)} *
      ((100vw - #{$min-vw}) / #{strip-unit($max-vw - $min-vw)})
    ),
    $max-size
  );
}

html {
  @include fluid-size(font-size, 320px, 3000px, 12px, 18.5px);

  @media screen and (max-width: 1800px) {
    font-size: 15px;
  }

  @media screen and (max-width: 1600px) {
    font-size: 14.5px;
  }

  @media screen and (max-width: 1460px) {
    font-size: 14px;
  }
}
*::-webkit-scrollbar-thumb {
  background:#0f0f14;
  border: solid 0.5px #393e47;
  border-radius: 50px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

/* reset default browser styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-user-select: none;
  /* Safari */
  -ms-user-select: none;
  user-select: none;
}

/* Change autocomplete styles in WebKit */

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus input:-webkit-autofill,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  box-shadow: none;
  -webkit-box-shadow: none;
  transition: background-color 5000s ease-in-out 0s;
}

input:-webkit-autofill {
  box-shadow: inset 0 0 0px 9999px white;
  -webkit-box-shadow: inset 0 0 0px 9999px white !important;
}


input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=search]::-webkit-search-cancel-button {
  display: none;
}

// html datepicker style

input[type="date"]::-webkit-datetime-edit,
input[type="date"]::-webkit-datetime-edit-month-field,
input[type="date"]::-webkit-datetime-edit-year-field {
  font-family: Noto Sans;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.6;
  text-align: left;
  color: #fff;
}

input::-webkit-datetime-edit-day-field:focus {
  background-color: inherit;
  color: #fff;
  outline: none;
}

::-webkit-calendar-picker-indicator {
  filter: invert(1);
}

.cp {
  cursor: pointer;
}

button {
  &[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

html,
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'Noto Sans', sans-serif;
  // max-width: 800px;
  height: 100%;

  &::-webkit-scrollbar {
    display: none;
  }

  // overflow: hidden;
}

.MuiTouchRipple-root {
  display: none;
}

button {
  cursor: pointer;
  background-color: transparent;
  border: none;

  &:focus {
    outline: none;
  }
}

.cp {
  cursor: pointer;
}

.w100 {
  width: 100%;
}

.dflex {
  display: flex;
}

.flexColunm {
  flex-direction: column;
}

.onboardingChk {
  .containerChk {
    .lblChk {
      font-weight: 300;
    }
  }

}

.containerChk {
  display: inline-block;
  position: relative;
  cursor: pointer;
  padding-left: 28px;
  text-align: left;

  &:focus-within {
    .checkmark {
      border: solid 1px #70ff00;
    }
  }

  .lblChk {
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    text-align: left;
  }

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .checkmark {
    position: absolute;
    top: 2px;
    left: 0;
    z-index: 1;
    width: 18px;
    height: 18px;
    border-radius: 1.7px;
    border: solid 0.7px #3b4665;
    background-color: #ebedf0;
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  input:checked~.checkmark {
    background-color: #70ff00;
    border: solid 0.7px #70ff00;
  }

  input:checked~.checkmark:after {
    display: block;
  }

  .checkmark:after {
    left: 6px;
    top: 3px;
    width: 3px;
    height: 7px;
    border: solid #000;
    border-width: 0 1.5px 1.5px 0;
    transform: rotate(45deg);
  }
}

.bryzos {
  position: absolute;
  top: 0px;
  left: 0px;
  padding: 5px 12px 0px 16px;
  z-index: 11;
  min-height: 42px;
}

.resetPosition {
  position: relative;
  // background-color: #000;
}

.loginBody {
  margin: auto;
  // width: 800px;
  // min-height: 108px;
  border-radius: 10px;
  position: relative;
  // backdrop-filter: blur(24px);
  // background-color: rgba(0, 0, 0, 0.6);

  table {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
    border-spacing: 0px;

    .inputBody {
      height: 108px;

      .enterEmail {
        .errorText {
          font-family: Noto Sans;
          font-size: 14px;
          line-height: 1.6;
          text-align: left;
          color: #f00;
        }

        .errorFont {
          font-size: 13px;
        }

        input {
          width: 100%;
          font-family: Noto Sans;
          font-size: 20px;
          line-height: 1.2;
          text-align: left;
          color: #fff;
          background-color: transparent;
          border: none;
          outline: none;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .togglePassWrapper {
          display: flex;
        }

      }

      #inputPlaceholder {
        width: 100%;
        font-family: Noto Sans;
        font-size: 20px;
        line-height: 1.2;
        text-align: left;
        color: rgba(255, 255, 255, 0.5);
        background-color: transparent;
        border: none;
        outline: none;
      }


      .passwordBox {
        display: flex;
        gap: 8px;

        input {
          border-bottom: 2px solid rgba(255, 255, 255, 0.5);
          font-family: Noto Sans;
          font-size: 20px;
          line-height: 1.2;
          text-align: center;
          color: #fff;
          width: 22px !important;
        }
      }

    }

    tr {
      td {
        height: 100%;
        padding: 0px 16px;
        padding-top: 46px;
        vertical-align: baseline;

        &:nth-child(1) {
          width: 65%;
          // background-color: rgba(0, 0, 0, 0.4);
          border-radius: 10px 0px 0px 10px;
        }

        &:nth-child(2) {
          width: 35%;
          // background-color: rgba(0, 0, 0, 0.75);
          border-radius: 0px 10px 10px 0px;
        }
      }
    }
  }



}

.drag {
  -webkit-app-region: drag;
}

.dragMain {
  display: flex;
  width: 100%;
  height: 25px;
  flex: 1;

  .stagingEnv {
    padding: 2px 10px;
    border-radius: 5000px;
    margin-left: 10px;
    background-image: linear-gradient(to right, #00c6ff, #0072ff);
    font-family: Noto Sans;
    font-size: 13px;
    color: #fff;
    line-height: 1.6;
  }

  .qaEnv {
    padding: 2px 10px;
    border-radius: 5000px;
    margin-left: 10px;
    background-image: linear-gradient(to right, #11998E, #38EF7D);
    font-family: Noto Sans;
    font-size: 13px;
    color: #fff;
    line-height: 1.6;
  }

  .demoEnv {
    padding: 2px 10px;
    border-radius: 5000px;
    margin-left: 10px;
    background-image: linear-gradient(to right, #ec008c, #fc6767);
    font-family: Noto Sans;
    font-size: 13px;
    color: #fff;
    line-height: 1.6;
  }

}

.signUpbtn {
  padding: 2px 8px;
  margin-right: 8px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  font-family: Noto Sans;
  font-size: 14px;
  line-height: 1.6;
  color: #fff;

  &:hover {
    background-color: rgba(255, 255, 255, 0.4);
    cursor: pointer;
  }
}

.bryzos {
  width: 100%;
  padding-bottom: 5px;
  display: flex;
  align-items: center;

  .headerTitle {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;

    a {
      text-decoration: none;
    }

    .bryzosTitle {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: 600;
      line-height: 1.4;
      text-align: left;
      color: #fff;
    }

    .addCursorPointer {
      cursor: pointer;
    }

    .bedgeLogout {
      width: 11px;
      height: 11px;
      background-color: #f00;
      border-radius: 50%;
      display: inline-flex;
      margin-left: 4px;
      position: relative;
      z-index: 1;
      top: 0px;
      transform: translate(0px, -0.5px);
    }

    .bedgeLogin {
      width: 11px;
      height: 11px;
      background-color: #42ff00;
      border-radius: 50%;
      display: inline-flex;
      margin-left: 4px;
      position: relative;
      z-index: 1;
      cursor: pointer;
      top: 0px;
      transform: translate(0px, -0.5px);
    }

  }

  #toggle-sticky-btn {
    .activePIn {
      display: none;
      margin-right: 12px;
    }

    &.pinFocus {


      .pinIcon {
        display: none;
        // background-color: rgba(255, 255, 255, 0.2);
        // border-radius: 2px;
      }

      .activePIn {
        display: block;
        margin-right: 12px;
        // background-color: rgba(255, 255, 255, 0.2);
        // border-radius: 2px;
      }
    }
  }


  .minimizeAppIcon {
    margin-right: 12px;
  }

  .shareAppIcon {
    margin-right: 12px;
    width: 100%;
    height: 100%;
  }

  .activeShareAppIcon {
    margin-right: 12px;
    width: 100%;
    height: 100%;

    svg {
      path {
        fill: #70ff00;
      }
    }
  }


  .headerText {
    font-family: Noto Sans;
    font-size: 14px;
    line-height: 1.2;
    text-align: left;
    color: #fff;
    margin-right: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .videoLibraryNavigation {
    display: flex;
    margin-top: 0px;

    svg {
      margin-right: 4px;
      display: flex;
      align-items: center;
    }
  }

  .videoLibraryActive {
    color: #70FF00;

    svg {
      path {
        fill: #70ff00;
      }
    }
  }

  .chatIcon {
    margin-top: 0px;
    padding: 2px;

    svg {
      opacity: 0.7;
    }

    &:disabled {
      display: none;

      svg {
        pointer-events: none;
      }
    }
  }

  .chatIconActive {
    svg {
      path {
        fill: #70ff00;
      }
    }
  }

  .showAlertForNewPo {
    background-color: #70ff00;
    color: rgb(0, 0, 0, 0.9);
    margin-top: 0;
    padding: 5px 8px;
    border-radius: 3px;
  }

  .headerTextActive {
    font-family: Noto Sans;
    font-size: 14px;
    line-height: 1.2;
    text-align: left;
    color: #70ff00;
    margin-right: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  .headerOrderActive {
    display: unset;
  }

  .headerDisabled {
    font-family: Noto Sans;
    font-size: 14px;
    line-height: 1.2;
    text-align: left;
    color: #fff;
    opacity: 0.5;
    margin-top: 5px;
    margin-right: 12px;
    cursor: not-allowed;
  }

  .buttoncut {
    display: flex;
    width: auto;
    text-align: right;
    margin-left: auto;
    white-space: nowrap;
    align-items: center;

    .impersonateIconStyle {
      color: white;
      font-size: 10px;
      cursor: pointer;
    }

    svg {
      transition: all 0.1s;
      cursor: pointer;
      width: 24px;
      height: 24px;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
      }
    }

    .minimizeAppIconDisabled {
      margin-right: 12px;
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }
    }
  }
}

.removePadding {
  padding: 0 !important;
}

.headerPanel {
  margin: auto;
  width: 100%;
  position: relative;
  opacity: 1;
  height: 100%;
}

.searchPanel {
  margin: auto;
  width: 800px;
  position: relative;

  .homeBody {
    border-radius: 10px;
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    // -webkit-backdrop-filter: blur(60px);
    // backdrop-filter: blur(60px);
    // background-color: rgba(0, 0, 0, 0.75);
    padding: 16px;
    // background: #000;

    &.bdrRadiusNone {
      border-radius: 0px;
    }


    input {
      font-family: Noto Sans;
      font-size: 20px;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      background-color: transparent;
      border: none;
      outline: none;
      width: 100%;

      &::placeholder {
        color: #fff;
        opacity: 0.5;
      }
    }
  }


}

.removePaddingBottom {
  padding-bottom: 0px;
}

.listBody {
  z-index: 99;
  width: 800px;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: unset !important;
  // background-color: rgba(0, 0, 0, 0.7);
  // backdrop-filter: blur(60px);
  padding: 12px 16px 12px 16px;
  border-radius: 0px 0px 10px 10px;

  .productListContainer {
    display: flex;
    flex-direction: row;
  }

  .ulBody {
    height: 100%;
    padding: 0 8px 0 0;
    overflow: auto;
    max-height: 425px;
    display: flex;
    flex-direction: column;
    grid-gap: 8px;
    scroll-behavior: smooth;
    flex: 1;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    

    .liBody {
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.4;
      color: #fff;
      padding: 6px 8px;
      border-radius: 4px;
      // height: 112px;
      border: 1px solid transparent;
      display: flex;
      flex-direction: column;
      justify-content: center;
      transition: all 0.1s;
      cursor: pointer;
      font-weight: 300;

      &:hover {
        border: 1px solid #42ff00;
      }

      &.selectedLiBody {
        border: 1px solid #42ff00;
      }

      .liHead {
        font-weight: 600;
      }
    }
  }

  .selectProduct {
    .headingSelect {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 0px 0px 0px 0px;

      &.footerSection {
        padding: 0px;
      }

      .firstDiv {
        flex: 1;
        display: flex;
        gap: 12px;

        .resetIcon {
          border-radius: 8px;
          background-color: rgba(0, 0, 0, 0.5);
          height: 40px;
          transition: all 0.1s;

          svg {
            width: 40px;
            height: 40px;
          }

          .img2 {
            display: none;
          }

          .img3 {
            position: absolute;
            left: 68px;
          }

          &:hover {
            .img1 {
              display: none;
            }

            .img2 {
              display: block;
            }

            .img3 {
              display: none;
            }
          }
        }

        .joinBryzos {
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
          text-align: left;
          color: #ccc;
          opacity: 0.5;
          transition: all 0.1s;
        }
      }

      .secondDiv {
        display: flex;
        gap: 18px;
        align-items: center;

        .comingSoon {
          font-family: Roboto;
          font-size: 17px;
          text-align: left;
          color: #42ff00;
        }

        .buyNowBtn {
          width: 104px;
          height: 40px;
          border-radius: 4px;
          border: solid 1px #fff;
          font-family: Roboto;
          font-size: 14px;
          line-height: 1.2;
          text-align: center;
          color: #fff;
          padding: 3px;
          cursor: pointer;
        }

        .buyNowBtnInactive {
          cursor: not-allowed;
        }

        .showNewPoCliam {
          background-color: #70ff00;
          border-color: #70ff00;
          color: #000;
          padding: 3px 15px;
        }

        a {
          text-decoration: none;
        }

        .shareWidget {
          font-family: Noto sans;
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
          text-align: left;
          color: #ccc;
          opacity: 0.5;
          transition: all 0.1s;

          &:hover {
            opacity: unset;
          }
        }
      }
    }


    .liBodyList {
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.4;
      color: #fff;
      font-weight: 400;
      padding: 8px 8px;
      border-radius: 8px;
      // height: 128px;
      width: 100%;
      border: 1px solid transparent;
      cursor: pointer;
      margin-bottom: 5px;
      transition: all 0.1s;

      /* HIDE RADIO */
      [type=radio] {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
      }

      &:hover {
        border: 1px solid #42ff00;
      }

      .liHead {
        font-weight: 600;
      }

      &.clickToShare {
        background-color: rgba(255, 255, 255, 0.75);
        color: #14181b;

        .priceFeedback {

          .hightBtn,
          .lowBtn {
            &:hover {
              svg {
                path {
                  fill: #70ff00;
                  opacity: unset;
                }
              }
            }
          }

          .CheckBtn {
            svg {
              path {
                fill: #14181b;
              }
            }

            &:hover {
              svg {
                path {
                  fill: #14181b !important;
                }
              }

            }
          }
        }

        .hightBtn,
        .lowBtn {
          svg {
            g {
              opacity: unset;
            }

            path {
              opacity: 0.3;
              fill: #14181b;
            }
          }
        }

        &:hover {
          border: 1px solid transparent;
        }

        .priceRating {

          .selectPrize {
            color: #000;
          }

          .notSelectPrize {
            color: #000;
          }
        }

        .liHead {
          font-weight: 600;
        }
      }

      .liHeadList {
        font-weight: 500;
      }

      .priceRating {
        display: flex;
        align-items: center;
        justify-content: right;
        text-align: right;
        gap: 12px;

        .selectPrize {
          font-family: Roboto;
          font-size: 32px;
          text-align: left;
          color: #fff;
          display: flex;
          align-items: flex-start;
          gap: 8px;
          line-height: 1.2;

          .doller {
            font-size: 24px;
            line-height: 1;
          }

          .prize {
            display: flex;
            align-items: flex-start;
            line-height: 1;
          }
        }

        .notSelectPrize {
          font-family: Roboto;
          font-size: 16px;
          color: #fff;
          line-height: 1.4;
        }

        label {
          height: 24px;
          display: flex;

          svg {
            height: 24px;
            width: 24px;
          }
        }

        .productFeedback {
          min-width: 24px;
        }
      }
    }
  }
}


.lineH {
  padding: 12px 0px;
  position: relative;

  &::after {
    content: "";
    width: 100%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
  }
}

.hidden {
  display: none;
}


// share widget page design 
.widgetBodyHeader {
  padding: 8px 16px 8px 16px;
  margin: auto;
  width: 800px;
  background-color: rgba(0, 0, 0, 0.9);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  //margin-top: 20px;

  .bryzos {
    margin: 0;
  }
}

.widgetBody {
  margin: auto;
  width: 800px;
  padding: 20px 24px;
  // -webkit-backdrop-filter: blur(60px);
  // backdrop-filter: blur(60px);
  // background-color: rgba(0, 0, 0, 0.75);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;

  &.tncBg {
    //     -webkit-backdrop-filter: blur(60px);
    // backdrop-filter: blur(60px);
    // background-color: rgba(0, 0, 0, 0.75);
  }

  .widgetHead {
    font-family: Noto Sans;
    font-size: 20px;
    font-weight: 500;
    line-height: 1.4;
    text-align: left;
    color: #70ff00;
    margin-bottom: 16px;
  }

  .widgetEmailInput {
    padding: 10px 12px;
    border-radius: 2px;
    border: solid 1px rgba(255, 255, 255, 0.5);
    background-color: #fff;
  }

  .widgetTextInput {
    height: 133px;
    margin-top: 25px;
    padding: 10px 12px;
    border-radius: 2px;
    border: solid 1px rgba(255, 255, 255, 0.5);
    background-color: #fff;
  }

  .widgetEmailInput,
  .widgetTextInput {
    input {
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.4;
      text-align: left;
      border: none;
      color: #14181b;
      outline: none;
      width: 100%;

      &::placeholder {
        color: rgba(0, 0, 0, 0.5);
      }
    }
  }

  .widgetButtons {
    text-align: right;
    margin-top: 16px;

    .cancelBtn {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: rgba(255, 255, 255, 0.3);
      background-color: transparent;
      border: none;
      margin-right: 40px;
      cursor: pointer;

      &:hover {
        color: #fff;
      }
    }

    .sendBtn {
      padding: 8px 32px;
      border-radius: 4px;
      background-color: #fff;
      font-family: Noto Sans;
      font-size: 18px;
      font-weight: 500;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #333;
      cursor: pointer;
      transition: all 0.1s;
      border: none;

      &:focus {
        outline: none;
      }

      &:hover {
        background-color: #70ff00;
        color: #000;
      }

      &:disabled {
        background-color: #70ff00;
        color: #000;
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .errorText1 {
    font-family: Noto Sans;
    font-size: 16px;
    line-height: 1.6;
    text-align: left;
    color: #f00;
    position: absolute;
  }

  // tnc 
  .joinBryzos {
    padding: 52px 40px;
    border-radius: 8px;
    box-shadow: inset 0 0 24px 6px rgba(0, 0, 0, 0.5);
    background-color: #fff;
    position: relative;

    .tncIcon {
      text-align: right;
      position: absolute;
      right: 8px;
      top: 10px;

      &:hover {
        .img1 {
          display: none;
        }

        .img2 {
          display: block;
        }
      }

      .img2 {
        display: none;
      }
    }

    .joinBryzosHead {
      font-family: Noto Sans;
      font-size: 24px;
      font-weight: bold;
      line-height: 1.6;
      text-align: left;
      color: #424656;

      span {
        font-family: Noto Sans;
        font-size: 24px;
        font-weight: bold;
        line-height: 1.6;
        text-align: left;
        color: #397aff;
        text-decoration: none;
        padding-left: 5px;
      }
    }

    .joinBryzosContent {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: #424656;
    }
  }
}

.smsTextShare {
  width: 100%;
  height: 100%;
  resize: none;
  font-family: Noto Sans;
  font-size: 16px;
  border: 0px;

  &:focus {
    outline: none;
  }

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }
}

.unitDropdown.unitDropdown {
  width: 82px;
  height: 40px;
  border-radius: 4px;

  .MuiSelect-select {
    padding: 10px 6px 10px 9px !important;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    border: 1px solid transparent;
    font-family: Roboto;
    font-size: 14px;
    font-weight: normal;
    line-height: normal;
    text-align: left;
    color: #fff;

    &[aria-expanded="true"] {
      border: 1px solid #fff;
    }
  }

  .MuiSelect-icon {
    top: unset;
    transform: unset;
    width: 20px;
    height: 20px;
  }

  fieldset {
    border: 0px;
    height: 100%;
  }
}

.multipleunitDropdown.multipleunitDropdown {
  width: 140px;
}

.SelectUnitDropdown.SelectUnitDropdown {
  padding: 8px;
  border-radius: 4px;
  -webkit-backdrop-filter: blur(6px);
  backdrop-filter: blur(6px);
  background-color: rgba(255, 255, 255, 0.7) !important;
  margin-top: 2px;
  background: url(assets/images/DropDownBG.png);

  ul {
    padding: 0px;

    li {
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #fff;
      padding: 4px 12px;
      border-radius: 4px;
      height: 29px;

      &.Mui-selected {
        background-color: transparent !important;
      }

      &:hover {
        background-color: #fff !important;
        color: #000;
      }
    }
  }

}

// success popup

.successBody {
  margin: auto;
  width: 800px;
  padding: 20px 24px;
  background-color: rgba(0, 0, 0, 0.75);
  -webkit-backdrop-filter: blur(60px);
  backdrop-filter: blur(60px);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  height: 332px;
  text-align: center;

  .greenText {
    font-family: Noto Sans;
    font-size: 24px;
    font-weight: 500;
    line-height: 1.4;
    color: #70ff00;
    padding-top: 84px;
  }

  .whiteText {
    font-family: Noto Sans;
    font-size: 20px;
    line-height: 1.4;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    cursor: pointer;
  }
}

.selectedPordListScroll {
  max-height: 285px;
  overflow: auto;
  padding-right: 6px;
  margin-top: 12px;

  &::-webkit-scrollbar {
    width: 8px;
    height: 6px;
  }
}

.priceFeedback {
  .CheckBtn {
    .img2 {
      display: none;
    }

    &:hover {
      .img1 {
        display: none;
      }

      .img2 {
        display: block;
      }
    }
  }

  .hightBtn,
  .lowBtn {
    &:hover {
      svg {
        g {
          opacity: unset;
        }

        path {
          fill: #70ff00;
          opacity: unset;
        }
      }
    }

  }

  .goodBtn {
    &:hover {
      svg {
        path {
          g {
            opacity: unset;
          }

          path {
            fill: #70ff00;
            opacity: unset;
          }
        }
      }
    }
  }
}

.TermsTitle {
  font-family: Noto Sans;
  font-size: 14px;
  font-weight: 900;
  line-height: 2;
  text-align: left;
  color: #525f7f;
  margin-bottom: 8px;
}

.ParaText {
  font-family: Noto Sans;
  font-size: 12px;
  font-weight: normal;
  line-height: 2;
  text-align: left;
  color: #525f7f;
  margin-bottom: 12px;
}

// tooltip css 
.MuiPopper-root,
.MuiTooltip-popper {
  .tooltipText {
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #000;
  }

  .shareappTooltip.shareappTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    border-radius: 3px;
    max-width: 205px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -6px;

    .MuiTooltip-arrow {
      height: 12px;
      width: 18px;
      color: rgba(255, 255, 255, 0.7);
      top: -4px;
    }
  }

  .priceUnitChangeTooltip.priceUnitChangeTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 3px;
    max-width: 262px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -11px;
    left: -7px;

    .MuiTooltip-arrow {
      height: 12px;
      width: 18px;
      color: rgba(255, 255, 255, 0.7);
      top: -4px;
      left: 5px !important;
    }
  }

  .chooseYourUnitTooltip.chooseYourUnitTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    border-radius: 3px;
    max-width: 232px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -5px;

    .MuiTooltip-arrow {
      height: 12px;
      width: 18px;
      color: rgba(255, 255, 255, 0.7);
      top: -4px;
    }
  }

  .orderPageTooltip.orderPageTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 8px 12px;
    border-radius: 3px;
    max-width: 307px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -8px;

    .MuiTooltip-arrow {
      height: 12px;
      width: 18px;
      color: rgba(255, 255, 255, 0.7);
      top: -4px;
    }
  }

  .signupTooltip.signupTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 3px;
    max-width: 360px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    top: -2px;

    .MuiTooltip-arrow {
      margin-right: -11px;
      height: 16px;
      width: 11px;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .shareWidgetTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 12px 16px !important;
    border-radius: 6px;
    max-width: 380px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);

    .shareWidgetText {
      font-family: Noto Sans;
      font-size: 18px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #000;
    }

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
      width: 25px;
      height: 16px;
      bottom: -8px !important;
      left: -15px !important;
    }
  }

  .priceFeedBakTooltip {
    background-color: #fff !important;
    padding: 8px 40px 8px 16px !important;
    border-radius: 6px;
    left: 15px;
    margin-bottom: 10px !important;
    max-width: 334px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);

    .headTooltip {
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: bold;
      text-align: left;
      color: #333;

      span {
        font-style: italic;
      }
    }

    .MuiTooltip-arrow {
      color: #fff;
      width: 23px;
      height: 18px;
      bottom: -8px !important;
      left: -15px !important;
    }
  }

  .rightTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 4px;
    max-width: 315px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 10px !important;


    .resetTooltipText {
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #000;
    }

    .MuiTooltip-arrow {
      width: 12px !important;
      height: 18px !important;
      left: -4px !important;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .acceptTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    max-width: 381px !important;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);

    .acceptText {
      font-family: Noto Sans;
      font-size: 12px;
      line-height: 1.4;
      text-align: left;
      color: #000;
    }

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
      width: 16px;
      height: 12px;
      top: -4px !important;
      left: 1px !important;
    }
  }

  .inputTooltip.inputTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 6px;
    max-width: 381px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 7px;
    margin-bottom: 7px;
    margin-top: 7px;
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
    color: #000;

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .addressTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    max-width: 381px !important;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 7px !important;
    margin-bottom: 7px !important;
    margin-top: 7px !important;
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
    color: #000;

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .partNumberTooltip {
    background-color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 6px !important;
    max-width: 381px !important;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 7px !important;
    margin-bottom: 7px !important;
    margin-top: 7px !important;
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.4;
    text-align: left;
    color: #000;

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
      left: -75px !important;
    }
  }

  .inputQtyTooltip {
    border-radius: 8px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.15);
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 0.7px;
    text-align: left;
    color: #fff;
    padding: 10px 12px;
    max-width: 194px;


    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.15);
      width: 16px;
      height: 12px;
      top: -2px !important;
      left: -26px !important;
    }
  }

  .stateTooltip {
    padding: 10px 12px;
    max-width: 194px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.4;
    letter-spacing: 0.7px;
    text-align: left;
    color: #fff;
    border-radius: 8px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.15);

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.15);
    }
  }

  .orderPreviewTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 12px;
    border-radius: 6px;
    max-width: 381px;
    -webkit-backdrop-filter: blur(6px);
    backdrop-filter: blur(6px);
    margin-left: 7px;
    margin-bottom: 7px !important;
    margin-top: 15px !important;


    .acceptText {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: #000;
    }

    .MuiTooltip-arrow {
      color: rgba(255, 255, 255, 0.7);
      height: 16px;
      width: 24px;
      top: -8px !important;

    }
  }
}

.containTooltip {
  font-family: Roboto;
  font-size: 18px;
  line-height: 1.6;
  text-align: left;
  color: #333;
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  gap: 5px;
}

// TnC

.TncContainer {
  width: 100%;
  height: 560px;
  padding: 32px 32px 32px 32px;
  border-radius: 8px;
  box-shadow: inset 0 0 24px 6px rgba(0, 0, 0, 0.5);
  background-color: #fff;
  overflow: hidden;

  .termsAndConditions {
    position: relative;
    overflow: auto;
    max-height: 495px;

    &::-webkit-scrollbar {
      width: 8px;
      height: 6px;
    }

    

    .tncIcon {
      text-align: right;
      position: absolute;
      right: 8px;
      top: 10px;

      &:hover {
        .img1 {
          display: none;
        }

        .img2 {
          display: block;
        }
      }

      .img2 {
        display: none;
      }
    }

    .termsAndConditionsHead {
      font-family: Noto Sans;
      font-size: 24px;
      font-weight: bold;
      line-height: 1.6;
      text-align: left;
      color: #525f7f;

      span {
        font-family: Noto Sans;
        font-size: 24px;
        font-weight: bold;
        line-height: 1.6;
        text-align: left;
        color: #397aff;
        text-decoration: none;
        padding-left: 5px;
      }
    }

    .tncHead {
      font-family: Noto Sans;
      font-size: 24px;
      font-weight: bold;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      margin-bottom: 8px;
    }

    .effectiveDate {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #424656;
      margin-bottom: 16px;
    }

    .TnCTitle {
      font-family: Noto Sans;
      font-size: 22px;
      font-weight: 600;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      margin-bottom: 4px;
    }

    .TnCTitle1 {
      font-size: 18px;
    }

    .TnCTitle2 {
      font-size: 14px;
    }

    .TnCPara {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      margin-bottom: 12px;
    }

    .TnCList {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      padding-left: 29px;
      margin-bottom: 4px;

      li {
        margin-bottom: 8px;
        list-style-type: disc;
      }
    }

    .TnCList1 {
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.6;
      text-align: left;
      color: #424656;
      padding-left: 29px;
      margin-bottom: 4px;

      li {
        margin-bottom: 8px;
        list-style-type: decimal;
      }
    }

    .termsAndConditionsContent {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: #525f7f;
    }

    .tncContent {
      font-family: Noto Sans;
      font-size: 18px;
      line-height: 1.6;
      text-align: left;
      color: #525f7f;
    }


  }
}

.tncOnboarding {
  color: #fff;

  .TnCTitle {
    font-family: Noto Sans;
    font-size: 22px;
    font-weight: 600;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    margin-bottom: 4px;
  }

  .TnCTitle2 {
    font-size: 14px;
  }

  .TnCPara {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    margin-bottom: 12px;
  }

  .TnCList {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    padding-left: 29px;
    margin-bottom: 4px;

    li {
      margin-bottom: 8px;
      list-style-type: disc;
    }
  }

  .TnCList1 {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    padding-left: 29px;
    margin-bottom: 4px;

    li {
      margin-bottom: 8px;
      list-style-type: decimal;
    }
  }
}

.tncButtons {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 20px;

  .disagreeBtn {
    font-family: Noto Sans;
    cursor: pointer;
    transition: all 0.1s;
    border: none;
    font-size: 20px;
    line-height: 1.6;
    color: #fff;
    background: transparent;
    margin-right: auto;
  }

  .agreeBtn {
    transition: all 0.1s;
    margin-left: auto;
    opacity: 0.5;
    font-family: Noto Sans;
    font-size: 20px;
    line-height: 1.6;
    text-align: center;
    color: #fff;
    background: transparent;
    cursor: not-allowed;
    border: 0px;

    &:focus {
      outline: none;
    }
  }

  .agreeBtnEnable {
    cursor: pointer;
    color: #70ff00;
    opacity: unset;
  }

  .downloadTnCBtn {
    font-family: Noto Sans;
    font-size: 14px;
    line-height: 1.5;
    text-align: center;
    color: #fff;
    transition: all 0.1s;
    cursor: pointer;
    margin-right: auto;

    &:hover {
      color: #70ff00;

    }
  }

  .agreeBtn1 {
    transition: all 0.1s;
    // margin-left: auto;
    font-family: Noto Sans;
    font-size: 20px;
    line-height: 1.6;
    text-align: center;
    color: #fff;
    background: transparent;
    border: 0px;
    cursor: pointer;

    &:focus {
      outline: none;
    }

    &:hover {
      color: #70ff00;
    }
  }
}

.mainContent {
  width: 100%;
  height: 100%;
  // -webkit-backdrop-filter: blur(60px);
  // backdrop-filter: blur(60px);
  // background-color: rgba(0, 0, 0, 0.75);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  min-height: 60px;
}

.qtyUnitDropdown.qtyUnitDropdown {
  .selectDropdown {
    display: flex;
    align-items: center;
  }
}

.selectDropdown {
  height: 100%;
  width: 100%;
  &.instantPriceSearchDropdown {
    .MuiSelect-icon {
      top: unset;
  }

  }

  .MuiSelect-icon {
    right: 0px;
    // top: calc(50% - 0.5em);
    transform: unset;
  }

  .MuiSelect-select {
    padding: 0px;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
  }

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  fieldset {
    border: 0px;
  }
}

.SelectDeliveryDate {
  padding-left: 10px;

  .MuiSelect-icon {
    right: 6px;
    top: calc(50% - 0.5em);
  }
}

.selectState {
  margin-left: -3px;

  .selectDropdown.selectDropdown {
    padding-right: 22px;
  }
}

.selectUploadCertDropdown.selectUploadCertDropdown {
  // height: 100%;
  width: 100%;
  height: 30px;
  .dropdownPlaceholderText {
    font-family: Syncopate;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.56px;
    text-align: left;
    color: #616575;
  }

  .selectDropdown.selectDropdown {
    padding-right: 22px;
  }

  .MuiSelect-icon {
    right: 3px;
    top: unset;
    transform: unset;
    display: flex;
    align-items: center;
  }

  .MuiSelect-select {
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0px 12px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.56px;
    text-align: left;
    color: #fff;
  }

  // &.Mui-focused {
  //   .selectDropdown.selectDropdown {
  //     border: solid 1px #71737f;
  //   }
  // }

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  fieldset {
    border: 0px;
  }
}
.selectExpirationDropdown.selectExpirationDropdown {
  .MuiSelect-icon {
    right: 12px;
    top: unset;
    transform: unset;
    display: flex;
    align-items: center;
  }
}

.selectReceivingHours.selectReceivingHours {
  width: 69px;
  height: 30px;
  margin-bottom: 8px;

  svg {
    display: none;
  }

  &.disabledDropdown.disabledDropdown {
    .selectDropdown {
      color: #fff;
    }
  }


  .selectDropdown.selectDropdown {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 6px 0;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.04);
    padding: 0px 18px 0px 2px;
    font-family: Inter;
    font-size: 12px;
    font-weight: 300;
    font-style: normal;
    line-height: 1;
    letter-spacing: 0.84px;
    text-align: left;
    color: #fff;
  }

  &.Mui-focused {
    .selectDropdown.selectDropdown {
      border: solid 1px #71737f;
    }
  }

  &.txtClosed.txtClosed {
    .selectDropdown {
      letter-spacing: -0.48px;
    }
  }


  fieldset {
    border: 0px;
  }
}

.btnExpiration.btnExpiration {
  width: 76px;
  height: 20px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 2px 3px;
  border-radius: 2px;
  font-family: Noto Sans;
  font-size: 10px;
  font-weight: normal;
  line-height: 1.6;
  text-align: left;
  color: #fff;

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  .selectDropdown.selectDropdown {
    padding: 0px 12px 0px 0px;
  }

  fieldset {
    border: 0px;
  }
}

.star-rating {
  display: inline-block;
  cursor: pointer;


  input {
    display: none;
  }

  label {
    position: relative;
    display: inline-block;
    width: 30px;
    height: 30px;
    background-image: url('./assets/images/StarOutlined.svg');
    background-size: cover;
    transition: background-image 0.1s ease-in-out;
  }

  input:checked+label {
    background-image: url('./assets/images/Star.svg');
  }

  label::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 0;
    animation: preload 0.1s 1;
  }

  /* Preload the images */
  @keyframes preload {
    0% {
      background-image: url('./assets/images/StarOutlined.svg');
    }

    100% {
      background-image: url('./assets/images/Star.svg');
    }
  }

}

.scrollContent {
  overflow: auto;
  max-height: 600px;
  padding-right: 4px;

  &.scrollSeller {
    max-height: 560px;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}

// .createPoScroll{
//   max-height: 540px;
//   padding-bottom: 3px;

//   &::-webkit-scrollbar {
//     display: none;
//   }

// }

.loaderMain {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 600px;

  &.loaderSeller {
    min-height: 560px;
  }

  &.loaderCreatePo {
    min-height: 650px;
  }
}

.loginLoader {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.isNotProd {
  background-color: #2196F3;
  border-radius: 6px;
}

.isDemo {
  background-color: rgba(33, 244, 77, 0.34);
  border-radius: 6px;
}

.datePickerCreatePO {
  width: 100%;
  padding-top: 0px;
  overflow: hidden;

  .MuiOutlinedInput-root {
    padding: 0px;
  }

  .MuiTextField-root {
    width: 100%;
  }

  .MuiIconButton-root {
    padding: 0px;
    margin: 0px;

    svg {
      path {
        fill: #fff
      }
    }
  }

  fieldset {
    border: 0px;
  }
}

.MuiPickersPopper-root {
  .MuiPaper-root {
    color: #fff;
    backdrop-filter: blur(24px);
    background-color: rgba(0, 0, 0, 0.25);
    margin-top: 10px;

    .MuiPickersArrowSwitcher-root {
      button {
        color: #fff;
      }

      .Mui-disabled {
        opacity: 0.5;
      }
    }

    .MuiDayCalendar-weekDayLabel {
      color: #fff;
      height: auto;
    }

    .MuiPickersFadeTransitionGroup-root {
      .MuiPickersDay-root {
        color: #fff;
      }

      .Mui-disabled {
        opacity: 0.5;
      }
    }

  }
}

.loaderClass {
  text-align: center;
}

.MuiAutocomplete-listbox.MuiAutocomplete-listbox {
  span.Mui-focused {
    border-radius: 10px;
    background-image: linear-gradient(335deg, #eaecf0 0%, #9b9eac 100%);
    color: #0f0f14;
  }

  span[aria-selected="true"].Mui-focused {
    border-radius: 10px;
    background-image: linear-gradient(335deg, #eaecf0 0%, #9b9eac 100%);
    color: #0f0f14;
  }
}

.resetPassword {
  border-radius: 10px 0px 0px 10px;
  padding: 4px 16px 12px 16px;
  display: flex;
  align-items: baseline;
  height: 100%;
  flex: 1;

  .resendOtp {
    font-family: Noto Sans;
    font-size: 12px;
    line-height: 1.6;
    text-align: left;
    color: #0072ff;
    background: transparent;
    border: none;
    cursor: pointer;
    margin-left: 5px;
  }

  .emailDiv {
    width: 100%;

    input {
      width: 100%;
      font-family: Noto Sans;
      font-size: 20px;
      line-height: 1.2;
      text-align: left;
      color: #fff;
      background-color: transparent;
      border: none;
      outline: none;
    }

    .errorText {
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.6;
      text-align: left;
      color: #f00;
    }
  }

  .forgetEmail {
    flex: 1;
    padding-right: 10px;
  }

  .forgetOtp {
    width: 180px;
  }

  .passDiv {
    width: 50%;
    margin-right: 0px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    &:nth-child(2) {
      padding-left: 12px;
      margin-right: 0px;
    }

    input {
      width: 100%;
      font-family: Noto Sans;
      font-size: 16px;
      line-height: 1.2;
      text-align: left;
      color: #fff;
      background-color: transparent;
      border: none;
      outline: none;
    }

    .errorText {
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.6;
      text-align: left;
      color: #f00;

      &.errorForgetPass {
        font-size: 12px;
      }
    }

    .showPassBtn {
      padding-left: 6px;
    }
  }

  .resetBtnDiv {
    width: 35%;
    display: flex;
    gap: 10px;
    justify-content: right;

    .resetPassBtn {
      padding: 8px 5px;
      border-radius: 4px;
      background-color: #70ff00;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      text-align: center;
      color: #000000;
      font-weight: 500;
      border: 1px solid transparent;
    }

    .nextBtn {
      padding: 8px 12px;
      border-radius: 4px;
      background-color: #70ff00;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      text-align: center;
      color: #000000;
      font-weight: 500;
      border: 1px solid transparent;
    }

    .backBtn {
      padding: 8px 12px;
      border-radius: 4px;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      font-weight: 500;
      border: 1px solid #fff;
    }
  }

  .passwordBox {
    display: flex;
    gap: 8px;

    input {
      border-bottom: 2px solid rgba(255, 255, 255, 0.5);
      font-family: Noto Sans;
      font-size: 20px;
      line-height: 1.2;
      text-align: center;
      color: #fff;
      width: 22px !important;
    }
  }
}

.forgot-password {
  position: relative;

  .forgot-password-btn {
    position: absolute;
    width: 135px;
    height: 26px;
    right: 68px;
    top: -34px;
    z-index: 1;
    padding: 2px 8px;
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: rgba(255, 255, 255, 0.5);
    cursor: pointer;

    &:hover {
      color: #fff;
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

.forgotPassText {
  font-family: Noto Sans;
  font-size: 14px;
  line-height: 1.6;
  text-align: left;
  color: #70ff00;
  cursor: pointer;
}

.UpdateButton {
  .MuiPaper-root {
    width: 100%;
    height: 100%;
    max-height: 100%;
    margin: 0;
    background-color: #000;
    display: flex;
    justify-content: center;
    align-items: center;

    button {
      font-size: 16px;
      color: #fff;
      border: 1px solid #fff;
      max-width: 200px;
      padding: 8px 16px;
      transition: all 0.1s;
      border-radius: 4px;

      &:hover {
        background-color: #fff;
        color: #000;
      }
    }
  }
}

.excitingNews {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  justify-content: center;
  color: #fff;
  padding: 15px;

  label {
    font-size: 16px;
    line-height: 1.2;
    font-weight: 500;
    padding-bottom: 6px;
  }

  p {
    font-size: 13px;
    text-align: center;
    line-height: 1.2;
    font-weight: 300;
  }

  span {
    font-size: 13px;
    line-height: 1.2;
    font-weight: 500;
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: #70ff00;
    }
  }
}

.selectDropdown1.selectDropdown1 {
  width: 380px;
  height: 40px;
  padding: 10px 0px;

  .dropdownPlaceholderText {
    font-weight: 300;
  }

  &.dropdownFocused {
    box-shadow: 0 0 25px 0 rgba(0, 0, 0, 0.5);
    background-color: rgba(255, 255, 255, 0.75);
    border-radius: 4px 4px 0px 0px;

    .MuiSelect-select {
      color: rgba(0, 0, 0, 0.75);
      background-color: rgba(255, 255, 255, 0.75);
    }

    .MuiSelect-icon {
      color: #474747;
    }

    fieldset {
      border-radius: 4px 4px 0px 0px;
      border: 0;
    }

  }

  &:hover {
    fieldset {
      border: solid 0.5px #fff;
    }
  }

  .MuiSelect-icon {
    right: 0px;
    transform: unset;
    font-size: 34px;
  }

  .MuiSelect-select {
    padding: 0 32px 0 10px;
    line-height: 46px;
    font-family: Noto Sans Display;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    color: #fff;
  }

  svg {
    color: #fff;
    right: -2px;
    transform: unset;
  }

  fieldset.MuiOutlinedInput-notchedOutline {
    border-radius: 4px;
    border: solid 0.5px #fff;
  }
}

.selectUserType {
  ul {
    li {
      &.Mui-focusVisible {
        background-color: transparent;
      }
    }
  }
}

.displayNone {
  display: none;
}


.ErrorDialog {
  .dialogContent {
    max-width: 300px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 30px 34px 30px 34px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;

    p {
      margin-bottom: 20px;
    }


    .submitBtn {
      height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 10px 24px;
      border-radius: 4px;
      border: solid 0.5px #fff;
      background-color: transparent;
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      margin-top: 20px;
      transition: all 0.1s;

      svg {

        g {
          path {
            fill: var(--logo-clr);
          }
        }

        path {
          fill-opacity: unset;
        }
      }

      &:hover {
        background-color: #70ff00;
        border: solid 0.5px #70ff00;
        color: #000;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          border: solid 0.5px #fff;
          background-color: transparent;
          color: #fff;
        }
      }
    }


  }

}

.questionIconPriceChange {
  display: flex;
  align-items: center;
  transition: all 0.1s;

  svg {
    height: 20px;
    width: 20px;
    cursor: pointer;
  }

  .questionIcon2 {
    display: none;
  }

  &:hover {
    .questionIcon1 {
      display: none;
    }

    .questionIcon2 {
      display: block;
    }
  }
}

.tooltipPopper.tooltipPopper {
  pointer-events: unset;

  &.tooltipSearchList {

    &[data-popper-placement*="bottom"] .tooltipRight2 {

      width: 265px;

      label {
        margin-top: 8px;
      }

      .tooltipArrow {
        &.MuiTooltip-arrow {
          left: 234px !important;
          top: -12px;
        }
      }
    }

    &[data-popper-placement*="top"] .tooltipRight2 {
      width: 265px;

      label {
        margin-top: 8px;
      }

      .tooltipArrow {
        &.MuiTooltip-arrow {
          left: 234px !important;
          top: auto;
          margin-bottom: -1em;
        }
      }
    }
  }

  .tooltipMain.tooltipMain {
    width: 208px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 5px 12px;
    border-radius: 3px;
    -webkit-backdrop-filter: blur(4.5px);
    backdrop-filter: blur(4.5px);
    background-color: rgba(255, 255, 255, 0.7);
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #000;
    margin-top: 8px;

    &.tooltipMainLogout {
      width: 228px;
      margin-top: 8px;
      padding: 8px 12px;
      left: -9px;

      .tooltipArrow {
        &.MuiTooltip-arrow {
          left: 6px !important;
          top: -12px;
          margin-top: 0px;
          width: 18px;
          height: 12px;

          &::before {
            width: 100%;
            height: 100%;
          }
        }
      }

      label {
        margin-top: 6px;
      }
    }

    &.tooltipCenter {
      width: 500px;
      min-width: 500px;
      padding: 3px 12px;
      margin-top: 4px;

      label {
        margin-top: 3px;
      }
    }

    &.tooltipRight {
      width: 450px;
      min-width: 450px;
      padding: 4px 8px 4px 12px;
      margin-top: 3px;

      &.tooltipRight1 {
        width: 312px;
        min-width: 312px !important;
      }

      label {
        margin-top: 3px;
      }
    }

    &.tooltipDesc {
      width: 183px;
      min-width: 183px;
      margin-bottom: 20px;
      left: 3px;

      .tooltipArrow {
        &.MuiTooltip-arrow {
          top: auto;
          margin-bottom: -1em;
        }
      }
    }

    &.tooltipSearch {
      width: 382px;
      min-width: 382px;
      padding: 8px 12px;
      margin-top: 26px;

      label {
        margin-top: 8px;
      }

      .tooltipArrow {
        &.MuiTooltip-arrow {
          left: -265px !important;
        }
      }

      &.tooltipPayment {
        width: 293px;
        min-width: 293px;

        .tooltipArrow {
          &.MuiTooltip-arrow {
            left: -115px !important;
          }
        }
      }

      &.tooltipAddRow {
        width: 208px;
        min-width: 208px;
        margin-top: 12px;

        .tooltipArrow {
          &.MuiTooltip-arrow {
            left: -178px !important;
          }
        }
      }

      &.tooltipRemove {
        width: 200px;
        min-width: 200px;
        margin-top: -18px;
        left: -11px;

        .tooltipArrow {
          &.MuiTooltip-arrow {
            left: 6px !important;
          }
        }
      }
    }

    .tooltipArrow {
      &.MuiTooltip-arrow {
        left: -4px !important;
        top: -12px;
        margin-top: 0px;
        width: 18px;
        height: 12px;

        &::before {
          width: 100%;
          height: 100%;
          background-color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }

  .tooltipMain2.tooltipMain2 {
    width: 230px;
    height: 78px;
    padding: 8px 12px;
    border-radius: 3px;
    -webkit-backdrop-filter: blur(4.5px);
    backdrop-filter: blur(4.5px);
    background-color: rgba(255, 255, 255, 0.7);
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: normal;
    line-height: 1.4;
    text-align: left;
    color: #000;
    margin-right: 12px;

    &.tooltipOrder {
      margin-top: -4px;

      .tooltipLeftArrow {
        &.MuiTooltip-arrow {
          margin-top: 4px;
        }
      }
    }

    .tooltipLeftArrow {
      &.MuiTooltip-arrow {
        width: 12px;
        height: 21px;
        color: rgba(255, 255, 255, 0.7);
        margin-right: -1em;
      }
    }

    label {
      margin-top: 8px;
    }
  }

  .tooltipMain3.tooltipMain3 {
    width: 204px;
    margin-top: 10px;
    margin-right: 0px;

    .tooltipBottomEndArrow {
      &.MuiTooltip-arrow {
        width: 22px;
        height: 14px;
        color: rgba(255, 255, 255, 0.7);
        top: -5px;
      }
    }
  }

  .tooltipMain4.tooltipMain4 {
    width: 227px;

    .tooltipLeftArrow1 {
      &.MuiTooltip-arrow {
        width: 12px;
        height: 21px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: -8px;
        margin-right: -1em;
      }
    }
  }

  .tooltipMain5.tooltipMain5 {
    width: 243px;
    margin-left: 12px;

    .tooltipLeftArrow1 {
      &.MuiTooltip-arrow {
        width: 12px;
        height: 21px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: -12px;
        margin-left: -1em;
      }
    }
  }

  .tooltipMain6.tooltipMain6 {
    margin-left: 15px;
    min-width: 359px;
    height: 61px;

    .tooltipLeftArrow1 {
      &.MuiTooltip-arrow {
        width: 12px;
        height: 21px;
        color: rgba(255, 255, 255, 0.7);
        left: -3.1px;
      }
    }
  }

  .tooltipMain7.tooltipMain7 {
    min-width: 337px;
    min-height: 95px;
    margin-top: 12px;
    margin-right: 0px;

    .tooltipLeftArrow1 {
      &.MuiTooltip-arrow {
        width: 19px;
        height: 13px;
        color: rgba(255, 255, 255, 0.7);
        top: -3.1px;
        margin-top: -10px;
      }
    }
  }

}

.pinTooltipPopper.pinTooltipPopper {
  .pinTooltip.pinTooltip {
    background-color: rgba(255, 255, 255, 0.7);
    padding: 6px 12px;
    border-radius: 3px;
    max-width: 347px;
    top: -8px;

    .pinText {
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: normal;
      line-height: 1.4;
      text-align: left;
      color: #000;
    }

    .arrowTooltip {
      &.MuiTooltip-arrow {
        width: 22px;
        height: 14px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: -1.29em;
        left: -5px !important;
      }
    }

  }
}
.multiCertTooltipPopper.multiCertTooltipPopper {
  .multiCertTooltip.multiCertTooltip {
    background-color: #d9d9d9;
    padding: 6px 12px;
    border-radius: 3px;
    max-width: 347px;
    top: -8px;
    font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: 0.56px;
  text-align: left;
  color: #0f0f14;

    .multiCertText {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.56px;
      text-align: left;
      color: #0f0f14;
    }

    .arrowTooltip {
      &.MuiTooltip-arrow {
        width: 22px;
        height: 14px;
        color: #d9d9d9;
        margin-top: -13px;
        left: -5px !important;
      }
    }

  }
}


.bgBackgroundColor {

  .loaderContent,
  .widgetBody,
  .tncBg,
  .bgBlurContent {
    -webkit-backdrop-filter: blur(60px);
    backdrop-filter: blur(60px);
    background-color: rgba(0, 0, 0, 0.75);
  }

  .listBody {
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(60px);
  }

  .searchPanel {
    .homeBody {
      -webkit-backdrop-filter: blur(60px);
      backdrop-filter: blur(60px);
      background-color: rgba(0, 0, 0, 0.75);
    }
  }

  .resetPosition {
    background-color: #000;
  }

  .loginBody {
    backdrop-filter: blur(24px);
    background-color: rgba(0, 0, 0, 0.6);

    table {


      tr {
        td {

          &:nth-child(1) {
            background-color: rgba(0, 0, 0, 0.4);
          }

          &:nth-child(2) {
            background-color: rgba(0, 0, 0, 0.75);
          }
        }
      }
    }
  }


  .bgBlurContent {
    .orderSearchBg {
      background-color: rgba(0, 0, 0, 0.9);
    }
  }

  .bgImg {
    background-image: url('/public/bg-app.png');
    display: flex;

  }
}

.dargPanelError {
  position: absolute;
  left: 1px;
  top: 1px;
  width: 90%;
  height: 45px;
}

.fetalErrorHeader {
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 2;

  button {
    transition: all 0.1s;
    display: inline-flex;

    &:first-child {
      margin-right: 12px;
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
    }
  }
}
.fatalErrorMain{
  flex:1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 100px);
}
.errorBoundaryMain {
  position: relative;
  width: 100%;
  height: 100%;

  // .errorBg {
  //   position: absolute;
  //   top: -24px;
  //   right: -24px;
  //   bottom: -24px;
  //   left: -24px;
  //   background: url(../renderer2/assets/New-images/Bg-App/imgBg3.png)no-repeat center center;
  //   background-size: cover;
  //   border-radius: 16px;
  // }
}

.errorPanel {
  display: flex !important;
  flex-direction: column;
  border-radius: 20px;
  height: 100%;
  align-items: center;
  justify-content: center;

  .errorPageHeader {
    height: 120px;
    width: 100%;
    display: flex;
    justify-content: left;
    align-items: center;
    padding-left: 32px;
    position: relative;
    overflow: hidden;
    background: url(assets/New-images/Header-No-Internet.svg) no-repeat;
    background-size: cover;
    background-position: center;

    .bgEllipsError {
      position: absolute;
      top: 0px;
      left: 0px;
      opacity: 0.56;
      -webkit-filter: blur(100px);
      filter: blur(100px);
      background-image: conic-gradient(from 0.25turn, #fff, rgba(255, 255, 255, 0) 0.19turn, #fff 0.4turn, rgba(255, 255, 255, 0) 0.55turn, #fff 0.76turn, rgba(255, 255, 255, 0) 0.97turn, #fff), linear-gradient(147deg, #e7ecef 75%, rgba(231, 236, 239, 0) 200%), linear-gradient(to bottom, #9786ff, #9786ff);
      width: 231.9px;
      height: 54.6px;
    }


  }

  .errorPage {
    width: 560px;
    height: 563px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-image: url('assets/New-images/New-Image-latest/noInternetBackground.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 60px 15px 40px 15px;

    .errorPageBryzos {
      font-family: Syncopate;
      font-size: 28px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: -1.12px;
      text-align: left;
      color: #fff;
      padding-bottom: 58px;
    }

    svg {
      width: 130px;
      height: 130px;
    }

    .fatalErrorTitle {
      font-family: Syncopate;
      font-size: 24px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: -0.96px;
      text-align: left;
      color: #fff;
      text-transform: uppercase;
      margin-top: 24px;
      margin-bottom: 8px;
    }

    p {
      font-family: Inter;
      font-size: 18px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.8);
    }

    .restartAppBtn {
      width: 220px;
      height: 50px;
      border-radius: 10px;
      background-color: #fff;
      font-family: Syncopate;
      font-size: 18px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.72px;
      text-align: center;
      color: #0f0f14;
      text-transform: uppercase;
      margin-top: 40px;
    }
  }
}

.backdropOverlay {
  background-color: rgba(0, 0, 0, 0.5) !important;
  position: absolute !important;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9999;
  border-radius: 16px;
}

.isbackdropOverlay {
  .bryzos {
    z-index: 10000;
  }
}

.companySelectDropdown.companySelectDropdown {
  height: 100%;
  width: 100%;
  padding: 0px;

  .MuiInputBase-root {
    height: 100%;

    &:focus {
      border-radius: 0px 0px 4px 4px;
    }
  }

  .MuiOutlinedInput-root {
    padding: 6px 6px 6px 16px;
    font-size: 14px !important;
    input{
      font-size: 14px !important;
    }
  }
  

  .MuiSelect-select {
    display: flex;
    justify-content: center;
  }


  svg {
    right: 24px;
    top: calc(50% - 0.6em);
    transform: unset;
    color: #fff;
  }

  fieldset {
    border: 0;
  }
}

.alert {
  background-color: #ed6a5e;
}

.warning {
  background-color: #f5bf4f;
}

.green {
  background-color: #61c654;
}

.snackbar_LOW {
  background-color: #ffd47b;
}

.snackbar_MEDIUM {
  background-color: #f5ad4f;
}

.snackbar_HIGH {
  background-color: #ff594a;
}

.turnOnNotif {
  margin-top: 24px;
  display: flex;
  justify-content: center;

  label {
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: 300;
    line-height: 1.4;
    text-align: left;
    color: #fff;

    .checkmark {
      top: 1px
    }
  }
}

// .snackBarMarginTop {
//   position: relative;
//   top: 63px;
//   left: 720px;
// }
.exportBtnSeller {
  font-family: "Noto Sans";
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 0.5px;
  // color: rgba(255, 255, 255, 0.7
  color: black;
  border: none;
  cursor: pointer;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.exportBtn {
  font-family: Syncopate;
  font-size: 11.8px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: -0.48px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  margin-right: 137px;

  &:hover {
    color: rgba(255, 255, 255, 0.9);
  }

  &:focus-visible {
    color: rgba(255, 255, 255, 0.9);
  }

  &[disabled] {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      color: rgba(255, 255, 255, 0.6);
    }

    &:focus-visible {
      color: rgba(255, 255, 255, 0.6);
    }
  }

  svg {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    fill: currentColor;
  }
}

  
.exportPoPdfOption {
  width: 100%;
  align-self: stretch;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  padding: 6px 12px;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.3;
  letter-spacing: normal;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);

  &:hover {
      border-radius: 6px;
      background-color: rgba(255, 255, 255, 0.2);
      color: #fff;
      font-weight: bold;
  }
}

.exportRightBtn {
  margin-right: 0px;
}

.acceptOrderExportBtn {
  .exportBtn {
    margin-top: 2px;
  }
}

// new tnc css 
.tncmaintitle {
  margin: 25px 0 24px;
  font-family: Inter;
  font-size: 24px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: normal;
  text-align: left;
  color: #fff;
}

.tncwelcometitle {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  margin-bottom: 20px;
  font-size: 20px;
  margin-top: 30px;
}

.tncnormaltext {
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #9b9eac;
  margin-bottom: 16px;
}

.tncliheading {
  margin-bottom: 20px;
}

.tncliheading::marker {
  font-weight: 600;
}

.tncliheading span {
  font-weight: 600;
}

.tncmargintop {
  margin-top: 10px;
}

.tncstyledecimal {
  list-style-type: decimal;
  margin-left: 40px;
}

.tncstylealpha {
  list-style-type: lower-alpha;
  margin-left: 30px;
}

.shareButtonText {
  text-align: right;
  font-family: Noto Sans;
  font-size: 10px;
  line-height: 1.2;
  letter-spacing: normal;
  color: #fff;
  margin-right: 12px;

  span {
    font-family: Noto Sans;
    font-size: 12px;
    font-weight: 600;
    line-height: 1.2;
    color: #16b9ff;
  }
}

.selectDropdown {
  .paymentPending {
    display: flex;
    flex-direction: column;
    position: relative;
    font-family: Noto Sans;

    span {
      position: absolute;
      top: -6px;
      left: 0;
    }

    i {
      position: absolute;
      top: 16px;
      left: -3px;
      font-size: 12px;
      font-weight: 300;
    }
  }
}

.react-emoji {
  .react-input-emoji--input {
    padding: 9px 39px 11px 12px;
  }

  .react-input-emoji--container {
    margin-right: 12px;
  }

  .react-input-emoji--button {
    position: absolute;
    right: 14px;
    background: transparent;
    z-index: 999;
  }
}

.noInternetWindow {
  height: 800px;
}

#thumb {
  display: none;
}

.shareBackdrop {
  filter: blur(4px);
}

.shareVideoMain.shareVideoMain {
  z-index: unset;

  .MuiModal-backdrop {
    display: none;
  }

  .MuiDialog-container {
    align-items: flex-start;
    margin-top: 174px;
  }

  .dialogContent {
    width: 100%;
    max-width: 480px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 40px 24px;
    border-radius: 10px;
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgb(0, 0, 0, 0.6);
    -webkit-backdrop-filter: blur(60px);
    backdrop-filter: blur(60px);
    margin: 0px;
    overflow: unset;
    border: 1px solid rgba(255, 255, 255, 0.15);
    z-index: 9999;

    .sharePopupContent {
      .closeShareIcon {
        position: absolute;
        top: 0px;
        right: 6px;
        cursor: pointer;
      }

      .titlePopup {
        font-family: Syncopate;
        font-size: 16px;
        font-weight: bold;
        line-height: 1.16;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
      }

      .iconSectionBox {
        margin-top: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        grid-column-gap: 12px;

        img {
          width: 40px;
        }

        button {
          display: flex;
          flex-direction: column;
          align-items: center;

          .btnTxt {
            font-family: Inter;
            font-size: 10px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: center;
            color: #fff;
            margin-top: 8px;
          }
        }
      }

      .activeShareBtn {
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.2);
        padding: 6px 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .shareBtn {
        padding: 6px 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .SendInputSection {
        margin-top: 18px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 12px;

        input {
          width: 330px;
          height: 40px;
          flex-grow: 0;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          padding: 0 12px;
          border-radius: 4px;
          border: solid 0.5px rgba(255, 255, 255, 0.2);
          background-color: #000;
          font-family: Noto Sans;
          font-size: 14px;
          font-weight: 600;
          font-stretch: normal;
          font-style: normal;
          line-height: 1;
          letter-spacing: normal;
          text-align: left;
          color: #fff;

          &::placeholder {
            opacity: 0.7;
            font-weight: 200;
          }

          &:focus {
            outline: none;
          }
        }

        button {
          width: 57px;
          height: 40px;
          flex-grow: 0;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          gap: 8px;
          padding: 13px 12px;
          border-radius: 4px;
          font-size: 14px;
          font-family: Noto Sans Display;
          font-weight: 600;
          transition: all 0.1s;
          background-color: #42ff00;
          color: #000;

          &[disabled] {
            cursor: not-allowed;
            background-color: rgba(255, 255, 255, 0.2);
            opacity: 0.5;
            color: #fff;
            font-weight: normal;

          }

        }
      }

      .emailError {
        font-family: Noto Sans;
        font-size: 14px;
        line-height: 1.6;
        text-align: left;
        color: #f00;
        position: absolute;
        margin-left: 8px;
      }

      .successMessageStyle {
        font-family: Noto Sans;
        font-weight: 300;
        font-size: 14px;
        line-height: 1.6;
        color: #70ff00;
        margin: 10px auto 0;
        width: calc(100% - 68px);
        text-align: center;
      }
    }
  }
}

.subTitleStyle {
  video::-webkit-media-text-track-container {
    height: calc(100% - 52px) !important;
    font-family: Inter;
    bottom: 45px !important;
  }

  video::cue {
    line-height: 1.5;
  }
}


.pressBtn {
  font-family: Noto Sans;
  font-size: 16px;
  line-height: 1.6;
  text-align: left;
  color: #2ecc71;
  background: transparent;
  border: none;
  cursor: pointer;
}

.impersonateDetail {
  font-family: Noto Sans;
  font-size: 12px;
  line-height: normal;
  text-align: center;
  font-weight: 600;
  color: #FF9800;
  position: absolute;
  left: 0px;
  top: 36px;
  background-color: rgba(233, 30, 99, 38%);
  padding: 0px 8px;
  width: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;

  /* Center the content */
  .message {
    margin-left: auto;
  }

  .ExitImpersonation {
    margin-left: auto;
    /* Move 'Exit' to the right */
    cursor: pointer;
    background: #a75d53;
    padding: 0 10px;
    border-radius: 10px;
    border: solid 0.5px;
    line-height: 1;
    display: flex;
    align-items: center;
    letter-spacing: 0.5px;
  }
}

/* ---------------------------------------------------------------------------------------------------------------------------------------------- */

[data-theme="dark"] {
  --W--01: #f0f0f0;
  --W--08: #ffffff0a;
  --W--01: #fff;
  --bdr-clr: transparent;
  --error-bg-dark: #720d16;
  --error-bg-light: #ff4859;
  --light-border-color: transparent;
  --placeholder: #616575;
  --bdr-img-clr: #8c8b99;
  --disabled-bg: rgba(255, 255, 255, 0.05);
  --background-color: #101015;
  --primary-color: rgba(151, 134, 255, 0.8);
  --secondary-color: rgba(151, 134, 255, 0.4);
  --tertiary-color: rgba(151, 134, 255, 0.6);
  --neutral-light: #e7ecef;
  --neutral-light-transparent: rgba(231, 236, 239, 0.4);
  --neutral-light-transparent-alt: rgba(231, 236, 239, 0);
  --dark-shadow: rgba(5, 4, 16, 0.32);
  --transparent-white: rgba(255, 255, 255, 0);
  --logo-clr: #fff;
  --resend-btn: #bff0b2;
  --box-shadow-focus: 2.2px 2.2px 2.2px 0 #000;
  --box-shadow-focus-error: inset -2.2px -2.2px 2.9px 0 #000, inset 2.2px 0 2.9px 0 #000;
  --animation-duration: 0.7s;
}

[data-theme="light"] {
  --W—01: #eee;
  --w-08: #f5f5f5;
  --placeholder: #d4d4d4;
  --background-color: white;
  --text-color: black;
  --light-border-color: #d1d1d1;
  --logo-clr: #000;
  --disabled-bg: #eee;
  --resend-btn: #74e654;
  --box-shadow-focus: 2px 2px 2px 0 #333;
  --bdr-clr: #d4d4d4;
  --error-bg-dark: #720d16;
  --bdr-img-clr: #8c8b99;
  --error-bg-light: #ff4859;
  --primary-color: rgba(151, 134, 255, 0.8);
  --secondary-color: rgba(151, 134, 255, 0.4);
  --tertiary-color: rgba(151, 134, 255, 0.6);
  --neutral-light: #e7ecef;
  --neutral-light-transparent: rgba(231, 236, 239, 0.4);
  --neutral-light-transparent-alt: rgba(231, 236, 239, 0);
  --dark-shadow: rgba(5, 4, 16, 0.32);
  --transparent-white: rgba(255, 255, 255, 0);
  --animation-duration: 0.7s;
}

.dragArea {
  -webkit-app-region: drag;
  width: 100%;
  height: 12px;
  background-color: transparent;
  position: absolute;
  top: 0px;
  left: 0px;

}

.loginWrapper {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 560px;
  padding: 80px 40px 40px;
  border-radius: 20px;
  column-gap: 38px;
  margin: 112px auto 261px auto;
  background: url(../renderer2/assets/New-images/New-Image-latest/Login-BG.svg) no-repeat;
  background-size: cover;

  .bryzosTitle {
    font-family: Syncopate;
    font-size: 28px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: -1.12px;
    text-align: left;
    color: #fff;
    margin-bottom: 40px;
  }

  input {
    width: 100%;
    padding: 0px 28px;
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.64px;
    text-align: left;
    color: #fff;
    background-color: rgba(255, 255, 255, 0.06);
  }

  .emailInput {
    position: relative;
    display: flex;
    flex-direction: column;
    margin-left: 0px;
    width: 100%;
    margin-bottom: 24px;
    transition: all 0.2s ease-in-out;

    input {
      transition: all 0.2s ease-in-out;
      
      &:focus {
        transform: scale(1);
      }
    }
  }

  .togglePassWrapper {
    width: 100%;
  }

  .loginInput {
    transition: all 0.2s ease-in-out;
    
    &:focus {
      background: url(../renderer2/assets/New-images/emailInput.svg);
      background-repeat: no-repeat;
      background-position: bottom right;
      background-size: cover;
      z-index: 2;
    }
  }

  .submitBtn {
    display: flex;

    button {
      width: 100%;
      height: 50px;
      margin: 80px 0 104px;
      padding: 14px 198px 12px 199px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: Syncopate;
      font-size: 20px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.2;
      letter-spacing: -0.8px;
      text-align: left;
      text-transform: uppercase;
      box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
      background-image: linear-gradient(115deg, #1c40e7 -7%, #16b9ff 106%);
      color: #fff;

      &:disabled {
        cursor: not-allowed;
        color: rgba(255, 255, 255, 0.2);
        background: url(../renderer2/assets/New-images/New-Image-latest/Login-Btn-BG.svg) no-repeat;
        box-shadow: none;
        background-size: cover;
      }
    }
  }

  .joinBtn {
    font-family: Syncopate;
    font-size: 16px;
    font-weight: bold;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #8c8b99;
    cursor: pointer;
    transition: all 0.1s;
    text-transform: uppercase;

    &:hover {
      color: #1fbbff;
    }

    &:focus {
      color: #1fbbff;
    }
  }

  .forgot-password-btn {
    position: absolute;
    bottom: -24px;
    cursor: pointer;
    left: 5px;
    font-family: Inter;
    font-size: 14px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: normal;
    text-align: right;
    color: #bff0b2;
  }

}

.resetPasswordWrapper {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 560px;
  padding: 80px 40px 40px;
  border-radius: 20px;
  background: url(../renderer2/assets/New-images/New-Image-latest/Login-BG.svg) no-repeat;
  column-gap: 38px;
  margin: 112px auto 261px auto;
  background-size: contain;

  .bryzosTitle {
    font-family: Syncopate;
    font-size: 28px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: -1.12px;
    text-align: left;
    color: #fff;
    margin-bottom: 40px;
  }


  .loginHelp {
    display: flex;
    column-gap: 40px;
    align-items: center;
    flex-direction: column;

    .loginHelpDesc {
      display: flex;
      flex-direction: column;
      align-items: center;
      row-gap: 16px;
      margin-bottom: 40px;

      .loginHelpTitle {
        font-family: Syncopate;
        font-size: 16.5px;
        font-weight: bold;
        line-height: normal;
        letter-spacing: 1.32px;
        text-align: left;
        color: var(--W--01);
        margin-bottom: 2px;
      }

      p {
        font-family: Inter;
        font-size: 14px;
        font-weight: 300;
        line-height: 1.25;
        letter-spacing: 0.36px;
        text-align: left;
        color: #fff;
        text-align: center;
      }
    }
  }

  .emailInput {
    display: flex;
    column-gap: 40px;
    align-items: center;
    flex-direction: column;
    width: 100%;

    .forgetEmailInput {
      width: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      input.EmailOtpInput {
        width: 100%;
        padding: 0px 28px;
        font-family: Inter;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.64px;
        text-align: left;
        color: #fff;
        background-color: rgba(255, 255, 255, 0.06);

        &:focus {
          background: url(../renderer2/assets//New-images/emailInput.svg);
          background-repeat: no-repeat;
          background-position: bottom right;
          background-size: cover;
          box-shadow: none;
          z-index: 2;
        }
      }
    }

    .submitBtn {
      display: flex;
      width: 100%;

      button {
        width: 100%;
        height: 50px;
        margin: 48px 0 81px;
        padding: 14px 20px 12px 20px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Syncopate;
        font-size: 20px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.2;
        letter-spacing: -0.8px;
        text-align: left;
        text-transform: uppercase;
        box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
        background-image: linear-gradient(115deg, #1c40e7 -7%, #16b9ff 106%);
        color: #fff;

        &:disabled {
          cursor: not-allowed;
          color: rgba(255, 255, 255, 0.2);
          background: url(../renderer2/assets/New-images/New-Image-latest/Login-Btn-BG.svg) no-repeat;
          box-shadow: none;
          background-size: contain;
        }
      }
    }
  }
  .resetBtnMain{
    width: 100%;
    display: flex;
  }


}

.forgetOtp.forgetOtp {
  display: flex;
  flex-direction: column;
  column-gap: 40px;
  width: 100%;

  .loginHelpDesc {
    p {
      width: 100%;
      display: flex;
      justify-content: center;
      font-family: Inter;
      font-size: 14px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 0.42px;
      text-align: center;
      color: #fff;
    }
  }

  .resetBtnMain {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    width: 100%;

    .resendOtp {
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: normal;
      text-align: left;
      color: #bff0b2;
      cursor: pointer;
      width: 100%;
    }

    .cancelReset {
      margin-top: 0px;
    }
  }

}

.togglePassWrapper {
  position: relative;
  display: flex;
  flex-direction: column;
}

.label {
  top: -11px;
  position: absolute;
  color: #fff;
  left: 23px;
}

.MuiSlider-root.MuiSlider-root {
  width: 3px;
  height: 140.5px;
  flex-grow: 0;
  color: #0e0e0e;
  padding: 0px 0px 0px 14px;
  border-radius: 0px;
}

.MuiSlider-root.MuiSlider-root {
  position: relative;

  .MuiSlider-thumb {
    width: 18px;
    height: 26px;
    border-radius: 4px;
    background-color: transparent;
    background-image: url(../renderer2/assets/New-images/DragButton.svg);
    background-repeat: no-repeat;
    background-position: 0px 1px;
    margin-left: 0px;
    box-shadow: 0 6px 8.4px 0 rgba(0, 0, 0, 0.55);

    &:hover {
      box-shadow: 0 6px 8.4px 0 rgba(0, 0, 0, 0.55)
    }

    &.Mui-focusVisible,
    &.Mui-active {
      box-shadow: 0 6px 8.4px 0 rgba(0, 0, 0, 0.55)
    }

    &::after {
      width: 100%;
      height: 100%;
      content: '';
    }

    &::before {
      box-shadow: none;
    }
  }

  .MuiSlider-rail,
  .MuiSlider-track {
    width: 3px;
    background-color: #0e0e0e;
  }
}

.MuiSlider-root.MuiSlider-root {
  .MuiSlider-markLabel {
    font-family: Syncopate;
    font-size: 10px;
    font-weight: normal;
    line-height: normal;
    letter-spacing: -0.5px;
    text-align: center;
    color: #fff;
    display: none;
  }

  .MuiSlider-mark {
    display: none;
  }
}


.unitFeedbackDropdown.unitFeedbackDropdown {
  display: flex;
  align-items: center;

  &.MuiInputBase-root {
    .MuiSelect-select {
      padding: 0px 0px 0px 8px;
      font-family: Inter;
      font-size: 14px;
      font-weight: 300;
      font-stretch: normal;
      line-height: 1;
      letter-spacing: 0.56px;
      text-align: left;
      color: #cacacc;
      min-height: auto;
      z-index: 2;
      width: 64px;
      height: 14px;
      background-color: transparent;
    }

    &.MuiOutlinedInput-notchedOutline {
      border: none
    }

    &:hover .MuiOutlinedInput-notchedOutline {
      border: none
    }

    &.Mui-focused .MuiOutlinedInput-notchedOutline {
      border: none
    }
  }
}

.SelectFeedbackDropdown.SelectFeedbackDropdown {
  border-radius: 4px;
  border: 0.5px solid #000;
  background-color: #cbcbcd;
  background-origin: border-box;
  background-clip: content-box, border-box;
  margin-top: -41px;
  box-shadow: none;
  overflow: unset;
  max-height: 100%;

  ul {
    padding: 0px 0px 0px 0px;

    li {
      padding: 0px 8px 0px 8px;
      font-family: Inter;
      font-size: 14px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 0.9;
      letter-spacing: 0.56px;
      text-align: left;
      color: #19191d;
      justify-content: flex-start;

      &:nth-child(3) {
        height: 16px;
      }

      &:nth-child(4) {
        height: 15.5px;
      }

      &.Mui-selected {
        background-color: #19191d;
        color: #fff;

        &:hover {
          background-color: #19191d;
          color: #fff;
        }
      }

      &:hover {
        background-color: rgba(55, 56, 61, 0.34);
        color: #19191d;
      }
    }
  }

}


.netTermsDropdown.netTermsDropdown {
  width: 100%;
  height: 50px;

  svg {
    path {
      fill: rgba(255, 255, 255, 0.6);
      fill-opacity: 1;
    }
  }

  .MuiSelect-icon.MuiSelect-icon {
    right: 20px;
    width: 20px;
    height: 21px;
    top: 14px;
  }

  .MuiSelect-select.MuiSelect-select {
    display: flex;
    align-content: center;
    justify-content: center;
    width: 100%;
    height: 50px;
    border-radius: 10px;
    border: solid 1px rgba(255, 255, 255, 0.25);
    background-color: #191a20;
    padding: 0px 0px;
    padding-right: 0px;
  }


  input {
    &:focus {
      outline: none;
    }
  }

  fieldset {
    border: unset;
  }
}

.net30TermError.net30TermError {

  .MuiSelect-select.MuiSelect-select {
    border: 1px solid transparent;
    box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
    background: linear-gradient(135deg, #720d16 -101%, #ff4859 83%) padding-box, linear-gradient(to bottom right, rgba(0, 0, 0, 0) 40%, rgba(255, 255, 255, 0.2901960784) 85%) border-box;

    .paymentMethod {
      color: #fff;
    }

    .paymentValue {
      color: #fff;
      font-weight: bold;
    }
  }
}

.liFisrtLine {
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.98px;
}

.datePickerPopper.datePickerPopper {
  .MuiPickersPopper-paper {
    box-shadow: inset 4px 4px 10.1px 0 #000;
    background-origin: border-box;
    background-clip: content-box, border-box;
    width: 250px;
    height: 200px;
    border-radius: 13px;
    border: solid 1px transparent;
    background: linear-gradient(#1f2127, #1f2026) padding-box, linear-gradient(to bottom right, rgba(0, 0, 0, 0) 54%, rgba(255, 255, 255, 0.2901960784) 85%) border-box;
    background-color: unset;
  }

  .MuiDateCalendar-root {
    .MuiPickersCalendarHeader-root {
      justify-content: center;
      margin-top: 0px;
      margin-bottom: 0px;
      min-height: 20px;

      .MuiPickersCalendarHeader-labelContainer {
        margin-right: 0px;
      }

    }

    .MuiPickersFadeTransitionGroup-root {
      .MuiDayCalendar-header {
        column-gap: 15px;

        .MuiTypography-root {
          font-family: Syncopate;
          font-size: 10px;
          font-weight: bold;
          line-height: normal;
          letter-spacing: 0.4px;
          text-align: center;
          color: #9b9eac;
        }
      }

      .MuiDayCalendar-weekDayLabel {
        height: 20px;
        padding: 0px;
        margin: 0px;
        width: 20px;
        font-family: Syncopate;
      }
    }

    .MuiPickersSlideTransition-root {
      height: 188px;

      .MuiDayCalendar {
        .MuiDayCalendar-monthContainer {
          height: 100%;
        }
      }

      .MuiDayCalendar-weekContainer {
        margin: 0px;
        column-gap: 15px;
        height: 20px;
        padding: 0px;
        margin: 0px;
        column-gap: 15px;
        margin: 3px 0px 3px 0px;

        .MuiPickersDay-root {
          height: 20px;
          font-family: Inter;
          font-size: 15px;
          font-weight: normal;
          line-height: normal;
          letter-spacing: 0.6px;
          text-align: center;
          color: #fff;
          width: 20px;
          margin: 0 0;
        }

        .Mui-disabled {
          opacity: unset;
          color: rgba(255, 255, 255, 0.1);

        }

        .Mui-selected {
          background-color: transparent;

          &::before {
            content: "";
            width: 16px;
            height: 16px;
            padding: 5px;
            background-color: #459fff;
            border-radius: 50%;
            position: absolute;
            z-index: -1;
          }

        }
      }
    }
  }
}

.datePickerPopper .MuiDateCalendar-root {
  width: 100%;
  padding: 10px 0px;
}

.btn {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin-right: 5px;
  position: relative;
  overflow: hidden;
  cursor: pointer;

  &:before,
  &:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 4px;
    opacity: 0;
    transition: all 300ms ease-in-out;
  }

  &:hover {

    &:before,
    &:after {
      top: 49%;
      opacity: 1;
      left: 49%;
    }
  }
}

.close-btn {
  background: #FF5D5B;
  border: 2px solid #CF544D;

  &:before,
  &:after {
    width: 1.5px;
    height: 100%;
    background: #000;
  }

  &:before {
    transform: translate(-50%, -50%) rotate(45deg);
  }

  &:after {
    transform: translate(-50%, -50%) rotate(-45deg);
  }
}

.min-btn {
  background: #FFBB39;
  border: 2px solid #CFA64E;

  &:before {
    width: 70%;
    height: 1.5px;
    background: #000;
  }
}

.EmailOtpInput {
  &:focus {
    box-shadow: inset -2.2px -2.2px 2.9px 0 #000, inset 2.2px 0 2.9px 0 #000;
    border: none;
    z-index: 2;
  }
}


.dropdownHeader {
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.4;
  letter-spacing: 0.98px;
  text-align: left;
  color: #616575;
  display: inline-flex;
  width: 100%;
  padding: 6px 16px 3px 16px;

  .childCount {
    background: none;
    font-family: Syncopate;
    font-weight: bold;
    color: #fff;
    padding: 0;

    &:hover {
      background: none;
      color: #fff;
    }
  }

  .matches {
    background: none;
    font-family: Syncopate;
    font-size: 12px;
    line-height: 1.4;
    letter-spacing: 0.84px;
    color: #fff;
    padding: 0;
    text-transform: uppercase;

    &:hover {
      background: none;
      color: #fff;
    }
  }
}

.isMacDevice {
  .scoreRow2 {
    margin-top: 0.2vh;
  }
}


.priceMainShare {
  .priceSelectedWrapper {
    display: flex;
    align-items: end;

    .priceSelected {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .priceSection {
        display: flex;

        .displayRow {
          display: flex;
          margin-left: 2px;
        }
      }
    }
  }
}

.selectDropdownBOM.selectDropdownBOM {
  width: 100px;
  height: 20px;
  position: absolute;
  top: 0px;
  left: 0px;
  visibility: hidden;

  .MuiSelect-select {
    padding: 0px;
    height: 100%;
  }
}

.oldTnc {
  .tncmaintitle {
    font-family: Syncopate;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 18px;
    margin-top: 40px;
    color: initial;
  }

  .tncwelcometitle {
    font-family: Inter, sans-serif;
    font-weight: 700;
    margin-bottom: 20px;
    font-size: 16px;
    margin-top: 40px;
    color: initial;
  }

  .tncnormaltext {
    font-family: Inter, sans-serif;
    font-size: 16px;
    color: initial;
  }

  .tncstyledecimal {
    list-style-type: decimal;
    margin-left: 40px;
  }

  .tncliheading {
    margin-bottom: 20px;
  }
}

.autocompleteContainer.autocompleteContainer {
  .MuiInputBase-root {
    width: 100%;
    height: 40px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px 0 16px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    color: #fff;
    border: 1px solid transparent;
  }

  .Mui-focused {
    .MuiInputBase-root {
      border: solid 1px #71737f;
      background-color: rgba(255, 255, 255, 0.04);

      &:focus {
        outline: none;
      }
    }


  }

  svg {
    fill: #fff
  }

  input {
    padding: 0px;
    font-size: 18px;
  }

  fieldset.MuiOutlinedInput-notchedOutline {
    border-color: transparent;
  }
}

.ag-root-wrapper{
  height: 100%;
}
.productSearchDropdown {
  width: 100%;
  fieldset {
    border: 0px;
  }
  .MuiSelect-select {
    padding: 0px;
  }
}

.ag-root-wrapper.ag-root-wrapper{
  border:0px;
  .ag-center-cols-container{
    width: 100% !important;
  }
  .ag-body-viewport{
   height:  calc(100% - 155px)
  }
}

.ag-header-cell, .ag-header-group-cell {
  padding: 0px 12px !important;     
  border-bottom: 4px solid #000;    
  background-color: #252529;
   font-family: Inter;
  font-size: 14px;
  font-weight: 600;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.18;
  letter-spacing: 0.28px;
  text-align: left;
  color: #fff;
}

.ag-center-cols-container .ag-row {
  border: none !important;         
}

.ag-cell {
  padding: 0px 12px !important;   
  border: none !important;      
   font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.47;
  letter-spacing: normal;
  text-align: left;
  color: #000;
  display: flex;
  align-items: center;
  
}
.ag-cell.ag-cell-inline-editing{
  background-color: transparent;
  background-image: unset;
}
.ag-input-field{
    background-color: transparent;
      border: solid 2.5px #226f44;
      &:focus{
        outline: none;
      }
  }


.setNewPasswordWrapper {
  border-radius: 13px;
  box-shadow: inset 4px 4px 10.1px 0 #000;
  background-image: linear-gradient(to bottom, #1b1c21, #1b1c21), linear-gradient(317deg, rgba(255, 255, 255, 0.1) 102%, #1a1b20 43%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  width: 100%;
  height: 124px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  overflow: hidden;
  z-index: 0;
   &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: inherit;
      padding: 0.7px;
      background: linear-gradient(to bottom right, #1a1b20 61%, #fff 294%);
      -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      background-clip: border-box;
      z-index: -1;
      pointer-events: none;
      left: 0;
      top: 0
    }


  .setNewPasswordInput {
    height: 50px;
    background: none;
    border: none;
  }
  .passwordRequirements {
    position: absolute;
    top: 50px;
    left: 1px;
    width: 99.50%;
    height: 24px;
    display: flex;
    padding: 0px 20px;
    flex-direction: row;
    z-index: 999;
    justify-content: space-between;
    align-items: center;
    border-image-source: linear-gradient(to top, #fff 120%, rgba(26, 27, 32, 0) 85%);
    border-image-slice: 1;
    background-color: #1b1c21;
    font-family: Inter;
    font-size: 11px;
    font-weight: normal;
    line-height: 1.23;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.5);
    z-index: 99;
    box-shadow: inset 4px 4px 10.1px 0 #000;
    overflow: hidden;

    &.passwordRequirements2 {
        top: 30px
    }
    .passwordRequirementItem {
      font-family: Noto Sans Display;
      font-size: 12px;
      font-weight: 300;
      line-height: 1.6;
      text-align: left;
    }

    .passwordRequirementItemActive {
        color: #32ff6c;
    }
  }
  .passwordRequirementsForChange {
    top: 51px;
  }
}
.changePasswordWrapper {
  background-size: contain;
}
.setNewPasswordContainer {
  text-align: center;
  text-transform: uppercase;
  .setNewPasswordHeader {
    font-family: Syncopate;
    font-size: 28px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: -1.12px;
    margin-bottom: 56px;
    color: #fff;
  }
  .setNewPasswordHeading {
    font-family: Syncopate;
    font-size: 16px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 1.28px;
    text-align: center;
    color: #fff;
    margin-bottom: 48px;
  }
  
}
.setNewPasswordButton {
  text-align: center;
  width: 100%;
  margin-top: 40px;
  margin-bottom: 40px;
  button {
    width: 100%;
    height: 50px;
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
    background-image: linear-gradient(115deg, #1c40e7 -7%, #16b9ff 106%);
    font-family: Syncopate;
    font-size: 20px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: -0.8px;
    text-align: center;
    color: #fff;
    text-transform: uppercase;
    &:disabled {
      cursor: not-allowed;
      border-radius: 12px;
      background-image: linear-gradient(115deg, rgba(255, 255, 255, 0.1) -7%, rgba(255, 255, 255, 0.1) 106%);
      box-shadow: none;
      margin-top: 40px;
    }
  }
}
.cancelReset {
  cursor: pointer;
  font-family: Inter;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #bff0b2;
  margin-top: 8px;
  text-align: right;
  width: 100%;
}
.resetErrorText{
  font-family: Inter;
  font-size: 14px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: left;
  color: #ff4848;
  margin-top: 8px;
  width: 100%;
  white-space: nowrap;
}
.hoverVideoPanel{
  background: url(assets/New-images/New-Image-latest/SharePricingPanel.svg) no-repeat !important;
  background-size: cover !important;
  background-position: center;
  background-repeat: no-repeat;
  font-family: Syncopate;
  font-size: 1rem;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: 0.07rem;
  text-align: left;
  color: rgba(255, 255, 255, 0.8);
}
.noHoverVideoPanel {
  font-family: Inter;
  font-size: 16px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.15;
  letter-spacing: normal;
  text-align: left;
  color: rgba(255, 255, 255, 0.8);
  width: 100%;
  height: 450px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  background-image: linear-gradient(162deg, #0f0f14 -26%, #393e47 226%);
}

.notificationPanel{
   padding: 16px 6px 16px 4px;
  border-radius: 16px;
  background-color: #222329;
  overflow: auto;
  .notificationPanelContainer {
    overflow-y: auto;
    padding-right: 6px;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }
  }
}
