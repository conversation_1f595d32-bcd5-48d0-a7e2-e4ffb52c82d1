import clsx from 'clsx';
import { useRightWindowStore } from './RightWindowStore';
import styles from './rightWindow.module.scss';
import {
  getChannelWindow,
  useGlobalStore,
  useBuyerSettingStore,
} from '@bryzos/giss-ui-library';
import { useRef, useEffect } from 'react';
import { ReactComponent as AcrylicCorner } from '../../assets/New-images/corner3.svg';
import VideoPlayerRightWindow from './VideoPlayerRightWindow/VideoPlayerRightWindow';
import CompleteYourAccountSetup from '../seller/CompleteYourAccountSetup/CompleteYourAccountSetup';
import { routes, userRole } from 'src/renderer2/common';
import { getNestedValue, isValidValue } from 'src/renderer2/helper';
import { useLocation } from 'react-router-dom';

const requiredFieldsToCompleteAccountSetup = {
  company: [
    'company_type',
    'company_address.line1',
    'company_address.city',
    'company_address.state_code',
    'company_address.state_id',
    'company_address.zip',
    'ap_contact_name',
    'ap_email_id',
    'send_invoices_to',
    'send_remittances_to',
  ], // all required
  user: ['first_name', 'last_name', 'email_id', 'phone', 'price_search_zip'],
  shipment: [ 'delivery_address',],
  resale: [  'resale_certificate',],
  payment: ['ach_credit', 'ach_debit', 'card'], // one of these is required
};


  
  // --- main validator ---
  function validateAccountSetup(account: any) {
    const errors: Record<string, string[]> = {
      company: [],
      user: [],
      shipment: [],
      resale: [],
      payment: [],
    };
  
    // 1. validate company, user, shipment, resale (all required)
    for (const section of ['company', 'user', 'shipment', 'resale'] as const) {
      for (const field of requiredFieldsToCompleteAccountSetup[section]) {
        const value = getNestedValue(account, field);
        if (!isValidValue(value)) {
          errors[section].push(field);
        }
      }
    }
  
    // 2. validate payment (at least one valid)
    const paymentFields = requiredFieldsToCompleteAccountSetup.payment;
    const hasValidPayment = paymentFields.some((field) =>
      isValidValue(account?.[field])
    );
  
    if (!hasValidPayment) {
      errors.payment = [...paymentFields]; // mark all as missing
    }
  
    // --- result ---
    const isValid = Object.values(errors).every((arr) => arr.length === 0);
    return { isValid, errors };
  }

const RightWindow = ({
  rightWindowRef,
  routerContentRef,
  updateBackdropOverlay,
}: {
  rightWindowRef: React.RefObject<HTMLDivElement>;
  routerContentRef: React.RefObject<HTMLDivElement>;
  updateBackdropOverlay: boolean;
}) => {
  const { userData } = useGlobalStore();
  const { buyerSetting } = useBuyerSettingStore();
  const isBuyer = userData?.data?.type === userRole.buyerUser;
  const location = useLocation();
  const showCompleteAccountSetupRoutes = [routes.homePage, routes.quotePage, routes.createPoPage, routes.orderManagementPage]
  const { loadComponent, toolTipVideoComponent, showVideo, setShowVideo } =
    useRightWindowStore();
  const channelWindow: any = getChannelWindow();

  useEffect(() => {
    if (rightWindowRef?.current && routerContentRef?.current) {
      const mainContentHeight = routerContentRef.current.clientHeight;
      const {
        isPriceSearchHistory,
        isSharedPricingHistory,
        isSharedAppHistory,
      } = useRightWindowStore.getState();
      rightWindowRef.current.style.maxHeight = `${mainContentHeight}px`;

      if (
        isPriceSearchHistory ||
        isSharedPricingHistory ||
        isSharedAppHistory
      ) {
        rightWindowRef.current.style.height = `${mainContentHeight}px`;
      } else {
        // For other pages, apply default height behavior
        rightWindowRef.current.style.height = '100%';
      }
    }
  }, [routerContentRef?.current?.clientHeight, loadComponent]);

  const { isValid, errors } = validateAccountSetup(buyerSetting);

  if (!(isBuyer && !isValid) && !loadComponent && !toolTipVideoComponent) {
    return null;
  }
  return (
    <div className={styles.rightWindowContainer}>
      {updateBackdropOverlay && <div className='backdropOverlay' />}
      <div className={styles.rightWindow} ref={rightWindowRef}>
        <div className={clsx('wrapperOverlay2', styles.wrapperOverlay)}></div>
        {loadComponent}
        {isBuyer && !isValid && showCompleteAccountSetupRoutes.includes(location.pathname) && <CompleteYourAccountSetup errors={errors} />}
        {/* {toolTipVideoComponent?toolTipVideoComponent:loadComponent} */}
        {/* {toolTipVideoComponent} */}
      </div>
    </div>
  );
};

export default RightWindow;
